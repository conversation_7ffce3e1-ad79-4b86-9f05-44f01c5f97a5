{"name": "@bonkai/auth", "version": "0.0.1", "private": true, "main": "src/index.ts", "types": "src/index.ts", "scripts": {"lint": "biome lint --write --unsafe", "format": "biome format --write"}, "dependencies": {"@clerk/nextjs": "^5.2.4", "@clerk/themes": "^2.1.12", "@solana/wallet-adapter-base": "^0.9.23", "@solana/wallet-adapter-react": "^0.15.35", "@solana/wallet-adapter-wallets": "^0.19.32", "@solana/web3.js": "^1.95.2"}, "peerDependencies": {"next": "^15.0.0", "react": "^18.0.0 || ^19.0.0"}}
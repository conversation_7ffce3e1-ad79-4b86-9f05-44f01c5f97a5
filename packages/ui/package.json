{"name": "@bonkai/ui", "version": "0.0.1", "private": true, "main": "src/index.tsx", "types": "src/index.tsx", "scripts": {"lint": "biome lint --write --unsafe", "format": "biome format --write"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "lucide-react": "^0.446.0", "tailwind-merge": "^2.5.2"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0", "react-dom": "^18.0.0 || ^19.0.0"}}
{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "dom", "dom.iterable"], "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "allowJs": true, "jsx": "react-jsx", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "incremental": true, "composite": true, "baseUrl": ".", "paths": {"@bonkai/ui": ["./packages/ui/src"], "@bonkai/ui/*": ["./packages/ui/src/*"], "@bonkai/auth": ["./packages/auth/src"], "@bonkai/auth/*": ["./packages/auth/src/*"], "@bonkai/ai": ["./packages/ai/src"], "@bonkai/ai/*": ["./packages/ai/src/*"], "@bonkai/blockchain": ["./packages/blockchain/src"], "@bonkai/blockchain/*": ["./packages/blockchain/src/*"], "@bonkai/types": ["./packages/types/src"], "@bonkai/types/*": ["./packages/types/src/*"]}}, "exclude": ["node_modules", "dist", ".next", "out", "build"]}
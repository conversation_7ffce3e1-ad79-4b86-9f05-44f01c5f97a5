/* eslint-disable */
/**
 * Generated utilities for implementing server-side Convex query and mutation functions.
 *
 * THIS CODE IS AUTOMATICALLY GENERATED.
 *
 * To regenerate, run `npx convex dev`.
 * @module
 */

import {
  ActionBuilder,
  HttpActionBuilder,
  MutationBuilder,
  QueryBuilder,
  internalActionGeneric,
  internalMutationGeneric,
  internalQueryGeneric,
  actionGeneric,
  httpActionGeneric,
  mutationGeneric,
  queryGeneric,
} from 'convex/server';
import type { DataModel } from './dataModel.js';

/**
 * Define a query in this Convex app's public API.
 *
 * This function will be allowed to read your Convex database and will be accessible from the client.
 *
 * @param func - The query function. It receives a `QueryCtx` as its first argument.
 * @returns The wrapped query. Include this as an export from your `convex/` directory.
 */
export declare const query: QueryBuilder<DataModel, 'public'>;

/**
 * Define a mutation in this Convex app's public API.
 *
 * This function will be allowed to modify your Convex database and will be accessible from the client.
 *
 * @param func - The mutation function. It receives a `MutationCtx` as its first argument.
 * @returns The wrapped mutation. Include this as an export from your `convex/` directory.
 */
export declare const mutation: MutationBuilder<DataModel, 'public'>;

/**
 * Define an action in this Convex app's public API.
 *
 * An action can call into third-party services and is accessible from the client.
 *
 * @param func - The action function. It receives an `ActionCtx` as its first argument.
 * @returns The wrapped action. Include this as an export from your `convex/` directory.
 */
export declare const action: ActionBuilder<DataModel, 'public'>;

/**
 * Define an HTTP action.
 *
 * This function will be used to respond to HTTP requests received by a Convex
 * HTTP action handler. Be sure to route requests to this function from
 * `convex/http.js`.
 *
 * @param func - The function. It receives an `ActionCtx` as its first argument,
 * and a `Request` object as its second.
 * @returns The wrapped HTTP action.
 */
export declare const httpAction: HttpActionBuilder<DataModel>;

/**
 * Define an internal query function.
 *
 * An internal query can be called by other Convex functions but is not
 * accessible from the client.
 *
 * @param func - The query function. It receives a `QueryCtx` as its first argument.
 * @returns The wrapped query.
 */
export declare const internalQuery: typeof internalQueryGeneric<
  DataModel,
  'internal'
>;

/**
 * Define an internal mutation function.
 *
 * An internal mutation can be called by other Convex functions but is not
 * accessible from the client.
 *
 * @param func - The mutation function. It receives a `MutationCtx` as its first argument.
 * @returns The wrapped mutation.
 */
export declare const internalMutation: typeof internalMutationGeneric<
  DataModel,
  'internal'
>;

/**
 * Define an internal action function.
 *
 * An internal action can be called by other Convex functions but is not
 * accessible from the client.
 *
 * @param func - The action function. It receives an `ActionCtx` as its first argument.
 * @returns The wrapped action.
 */
export declare const internalAction: typeof internalActionGeneric<
  DataModel,
  'internal'
>;

/**
 * Export these helpers from `convex/_generated/server.js` to use them in your
 * Convex app.
 */
export {
  queryGeneric,
  mutationGeneric,
  actionGeneric,
  httpActionGeneric,
  internalQueryGeneric,
  internalMutationGeneric,
  internalActionGeneric,
};

import { v } from 'convex/values';
import { mutation, query } from './_generated/server';
import { Id } from './_generated/dataModel';

// Link Telegram user to existing BonKai user
export const linkTelegramUser = mutation({
  args: {
    userId: v.id('users'),
    telegramId: v.string(),
    username: v.optional(v.string()),
    firstName: v.optional(v.string()),
    lastName: v.optional(v.string()),
    languageCode: v.optional(v.string()),
    isPremium: v.boolean(),
  },
  handler: async (ctx, args) => {
    // Check if Telegram user is already linked
    const existing = await ctx.db
      .query('telegramUsers')
      .withIndex('by_telegram_id', (q) => q.eq('telegramId', args.telegramId))
      .first();

    if (existing && existing.userId !== args.userId) {
      throw new Error('Telegram account already linked to another user');
    }

    if (existing) {
      // Update existing record
      await ctx.db.patch(existing._id, {
        username: args.username,
        firstName: args.firstName,
        lastName: args.lastName,
        languageCode: args.languageCode,
        isPremium: args.isPremium,
      });
      return existing._id;
    }

    // Create new link
    return await ctx.db.insert('telegramUsers', {
      userId: args.userId,
      telegramId: args.telegramId,
      username: args.username,
      firstName: args.firstName,
      lastName: args.lastName,
      languageCode: args.languageCode,
      isPremium: args.isPremium,
      createdAt: new Date().toISOString(),
    });
  },
});

// Get user by Telegram ID
export const getUserByTelegramId = query({
  args: { telegramId: v.string() },
  handler: async (ctx, args) => {
    const telegramUser = await ctx.db
      .query('telegramUsers')
      .withIndex('by_telegram_id', (q) => q.eq('telegramId', args.telegramId))
      .first();

    if (!telegramUser) return null;

    const user = await ctx.db.get(telegramUser.userId);
    if (!user) return null;

    return {
      ...user,
      telegram: telegramUser,
    };
  },
});

// Unlink Telegram account
export const unlinkTelegramUser = mutation({
  args: { telegramId: v.string() },
  handler: async (ctx, args) => {
    const telegramUser = await ctx.db
      .query('telegramUsers')
      .withIndex('by_telegram_id', (q) => q.eq('telegramId', args.telegramId))
      .first();

    if (!telegramUser) {
      throw new Error('Telegram account not found');
    }

    await ctx.db.delete(telegramUser._id);
    return { success: true };
  },
});

// Track token usage for Telegram user
export const trackTokenUsage = mutation({
  args: {
    userId: v.id('users'),
    tokens: v.number(),
    model: v.string(),
  },
  handler: async (ctx, args) => {
    const now = new Date();
    const timestamp = now.toISOString();
    const month = timestamp.slice(0, 7); // YYYY-MM

    await ctx.db.insert('tokenUsage', {
      userId: args.userId,
      tokens: args.tokens,
      model: args.model,
      timestamp,
      month,
    });

    // Update user's total token balance
    const user = await ctx.db.get(args.userId);
    if (user) {
      await ctx.db.patch(args.userId, {
        tokenBalance: Math.max(0, user.tokenBalance - args.tokens),
        updatedAt: timestamp,
      });
    }
  },
});

// Check rate limit for Telegram user
export const checkRateLimit = query({
  args: {
    userId: v.id('users'),
    tier: v.string(),
  },
  handler: async (ctx, args) => {
    const now = new Date();
    const windowStart = new Date(now.getTime() - 60 * 60 * 1000); // 1 hour window

    // Get rate limit record
    const rateLimit = await ctx.db
      .query('rateLimits')
      .withIndex('by_user', (q) => q.eq('userId', args.userId))
      .first();

    const limits = {
      FREE: 10,
      BRONZE: 50,
      SILVER: 200,
      DIAMOND: 1000,
    };

    const limit = limits[args.tier as keyof typeof limits] || 10;

    if (!rateLimit || new Date(rateLimit.windowStart) < windowStart) {
      // Create new rate limit window
      await ctx.db.insert('rateLimits', {
        userId: args.userId,
        requests: 1,
        windowStart: now.toISOString(),
        tier: args.tier,
      });

      return {
        allowed: true,
        remaining: limit - 1,
        resetAt: now.getTime() + 60 * 60 * 1000,
      };
    }

    if (rateLimit.requests >= limit) {
      return {
        allowed: false,
        remaining: 0,
        resetAt: new Date(rateLimit.windowStart).getTime() + 60 * 60 * 1000,
      };
    }

    // Increment request count
    await ctx.db.patch(rateLimit._id, {
      requests: rateLimit.requests + 1,
    });

    return {
      allowed: true,
      remaining: limit - rateLimit.requests - 1,
      resetAt: new Date(rateLimit.windowStart).getTime() + 60 * 60 * 1000,
    };
  },
});

// Get user stats for Telegram
export const getTelegramUserStats = query({
  args: { telegramId: v.string() },
  handler: async (ctx, args) => {
    const telegramUser = await ctx.db
      .query('telegramUsers')
      .withIndex('by_telegram_id', (q) => q.eq('telegramId', args.telegramId))
      .first();

    if (!telegramUser) return null;

    const user = await ctx.db.get(telegramUser.userId);
    if (!user) return null;

    // Get chat count
    const chats = await ctx.db
      .query('chats')
      .withIndex('by_user', (q) => q.eq('userId', telegramUser.userId))
      .collect();

    // Get current month token usage
    const currentMonth = new Date().toISOString().slice(0, 7);
    const tokenUsage = await ctx.db
      .query('tokenUsage')
      .withIndex('by_month', (q) => q.eq('month', currentMonth))
      .filter((q) => q.eq(q.field('userId'), telegramUser.userId))
      .collect();

    const totalTokensUsed = tokenUsage.reduce(
      (sum, usage) => sum + usage.tokens,
      0,
    );

    // Get token limits based on tier
    const tokenLimits = {
      FREE: 10_000,
      BRONZE: 100_000,
      SILVER: 500_000,
      DIAMOND: 2_000_000,
    };

    const tokenLimit = tokenLimits[user.tier] || tokenLimits.FREE;

    return {
      user: {
        ...user,
        telegram: telegramUser,
      },
      stats: {
        chatCount: chats.length,
        tokensUsedThisMonth: totalTokensUsed,
        tokenLimit,
        tokensRemaining: tokenLimit - totalTokensUsed,
        tier: user.tier,
        tokenBalance: user.tokenBalance,
        walletAddress: user.walletAddress,
        walletLinked: !!user.walletAddress,
      },
    };
  },
});

// Create linking code for authentication
export const createLinkingCode = mutation({
  args: {
    telegramId: v.string(),
    code: v.string(),
  },
  handler: async (ctx, args) => {
    // Store in a temporary collection (you might want to add this to schema)
    // For now, we'll use a simple approach with expiration
    const expiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    // In a real implementation, you'd store this in a proper collection
    // For now, we'll return the data to be stored in memory/cache
    return {
      code: args.code,
      telegramId: args.telegramId,
      expiresAt: expiresAt.toISOString(),
    };
  },
});

// Verify linking code and complete authentication
export const verifyLinkingCode = mutation({
  args: {
    code: v.string(),
    clerkId: v.string(),
  },
  handler: async (ctx, args) => {
    // In a real implementation, you'd fetch from a temporary collection
    // For now, this will be handled by the bot's in-memory storage

    // Get user by Clerk ID
    const user = await ctx.db
      .query('users')
      .withIndex('by_clerk_id', (q) => q.eq('clerkId', args.clerkId))
      .first();

    if (!user) {
      throw new Error('User not found');
    }

    return {
      success: true,
      userId: user._id,
      user,
    };
  },
});

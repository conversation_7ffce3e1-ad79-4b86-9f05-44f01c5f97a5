{"name": "bonkai", "version": "1.0.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "lint": "turbo run lint", "test": "turbo run test", "format": "turbo run format", "db:generate": "turbo run db:generate", "db:migrate": "turbo run db:migrate", "db:studio": "turbo run db:studio"}, "devDependencies": {"turbo": "^2.5.4", "@biomejs/biome": "^1.9.4", "typescript": "^5.6.3"}, "packageManager": "bun@1.2.15"}
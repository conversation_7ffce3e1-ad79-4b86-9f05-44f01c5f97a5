{"name": "bonkai", "version": "1.0.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "lint": "turbo run lint", "test": "turbo run test", "format": "turbo run format", "convex:dev": "convex dev", "convex:deploy": "convex deploy", "convex:dashboard": "convex dashboard", "convex:auth": "convex auth", "web:dev": "cd apps/web && bun run dev", "web:build": "cd apps/web && bun run build", "web:start": "cd apps/web && bun run start", "bot:dev": "cd apps/telegram-bot && bun run dev", "bot:build": "cd apps/telegram-bot && bun run build", "bot:start": "cd apps/telegram-bot && bun run start", "bot:webhook": "cd apps/telegram-bot && bun run webhook", "bot:deploy": "cd apps/telegram-bot && ./deploy.sh"}, "dependencies": {"convex": "^1.25.2"}, "devDependencies": {"turbo": "^2.5.4", "@biomejs/biome": "^1.9.4", "typescript": "^5.6.3"}, "packageManager": "bun@1.2.15"}
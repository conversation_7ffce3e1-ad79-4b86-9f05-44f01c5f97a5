'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@clerk/nextjs';
import { useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, CheckCircle, XCircle, ExternalLink } from 'lucide-react';

export default function TelegramLinkPage() {
  const { isSignedIn, isLoaded } = useAuth();
  const searchParams = useSearchParams();
  const codeFromUrl = searchParams.get('code');

  const [code, setCode] = useState(codeFromUrl || '');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
  } | null>(null);

  const handleLink = async () => {
    if (!code.trim()) {
      setResult({
        success: false,
        message: 'Please enter a valid linking code',
      });
      return;
    }

    setLoading(true);
    setResult(null);

    try {
      const response = await fetch('/api/telegram/link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ code: code.trim() }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setResult({
          success: true,
          message: data.message,
        });
      } else {
        setResult({
          success: false,
          message: data.error || 'Failed to link account',
        });
      }
    } catch (error) {
      setResult({
        success: false,
        message: 'Network error. Please try again.',
      });
    } finally {
      setLoading(false);
    }
  };

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loader2 className="size-8 animate-spin" />
      </div>
    );
  }

  if (!isSignedIn) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <CardTitle>Link Telegram Account</CardTitle>
            <CardDescription>
              Please sign in to link your Telegram account to BonKai
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Alert>
              <AlertDescription>
                You need to be signed in to link your Telegram account. Please
                sign in first, then return to complete the linking process.
              </AlertDescription>
            </Alert>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <CardTitle className="flex items-center justify-center gap-2">
            <span>🔗</span>
            Link Telegram Account
          </CardTitle>
          <CardDescription>
            Connect your Telegram account to BonKai AI
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {!result?.success && (
            <>
              <div className="space-y-2">
                <label htmlFor="code" className="text-sm font-medium">
                  Linking Code
                </label>
                <Input
                  id="code"
                  type="text"
                  placeholder="Enter 8-character code from bot"
                  value={code}
                  onChange={(e) => setCode(e.target.value.toUpperCase())}
                  className="text-center font-mono"
                  maxLength={10}
                />
              </div>

              <Button
                onClick={handleLink}
                disabled={loading || !code.trim()}
                className="w-full"
              >
                {loading ? (
                  <>
                    <Loader2 className="size-4 animate-spin mr-2" />
                    Linking Account...
                  </>
                ) : (
                  'Link Account'
                )}
              </Button>
            </>
          )}

          {result && (
            <Alert
              className={
                result.success
                  ? 'border-green-200 bg-green-50'
                  : 'border-red-200 bg-red-50'
              }
            >
              <div className="flex items-start gap-2">
                {result.success ? (
                  <CheckCircle className="size-4 text-green-600 mt-0.5" />
                ) : (
                  <XCircle className="size-4 text-red-600 mt-0.5" />
                )}
                <AlertDescription
                  className={result.success ? 'text-green-800' : 'text-red-800'}
                >
                  {result.message}
                </AlertDescription>
              </div>
            </Alert>
          )}

          {result?.success && (
            <div className="space-y-3">
              <Button
                onClick={() =>
                  window.open('https://t.me/bonkai_ai_bot', '_blank')
                }
                className="w-full"
                variant="outline"
              >
                <ExternalLink className="size-4 mr-2" />
                Open Telegram Bot
              </Button>

              <Button
                onClick={() => {
                  setResult(null);
                  setCode('');
                }}
                className="w-full"
                variant="ghost"
              >
                Link Another Account
              </Button>
            </div>
          )}

          <div className="text-center text-sm text-gray-600">
            <p>Don&apos;t have a linking code?</p>
            <p>
              Start a chat with{' '}
              <a
                href="https://t.me/bonkai_ai_bot"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:underline"
              >
                @bonkai_ai_bot
              </a>{' '}
              and use /link
            </p>
          </div>

          <div className="bg-gray-50 p-3 rounded-lg text-xs text-gray-600 space-y-1">
            <p className="font-semibold">How to link:</p>
            <ol className="list-decimal list-inside space-y-1">
              <li>Open Telegram and find @bonkai_ai_bot</li>
              <li>Send /link command to get your code</li>
              <li>Enter the code above to complete linking</li>
              <li>Start chatting with BonKai AI!</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

import 'server-only';

import { api } from '@/convex/_generated/api';
import type { Id } from '@/convex/_generated/dataModel';
import { ConvexHttpClient } from 'convex/browser';
import type { ArtifactKind } from '@/components/artifact';
import type { VisibilityType } from '@/components/visibility-selector';
import { ChatSDKError } from '../errors';

// Initialize Convex client for server-side operations
const convex = new ConvexHttpClient(process.env.NEXT_PUBLIC_CONVEX_URL!);

// User operations
export async function getUserByClerkId(clerkId: string) {
  try {
    return await convex.query(api.users.getUserByClerkId, { clerkId });
  } catch (error) {
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get user by clerk id',
    );
  }
}

export async function createUser({
  clerkId,
  email,
  name,
  imageUrl,
  walletAddress,
}: {
  clerkId: string;
  email: string;
  name: string;
  imageUrl?: string;
  walletAddress?: string;
}) {
  try {
    return await convex.mutation(api.users.upsertUser, {
      clerkId,
      email,
      name,
      imageUrl,
      walletAddress,
      tier: 'FREE',
    });
  } catch (error) {
    throw new ChatSDKError('bad_request:database', 'Failed to create user');
  }
}

// Chat operations
export async function saveChat({
  userId,
  title,
  visibility,
}: {
  userId: Id<'users'>;
  title: string;
  visibility: VisibilityType;
}) {
  try {
    return await convex.mutation(api.chats.createChat, {
      userId,
      title,
      visibility,
    });
  } catch (error) {
    throw new ChatSDKError('bad_request:database', 'Failed to save chat');
  }
}

export async function deleteChatById({
  chatId,
  userId,
}: {
  chatId: Id<'chats'>;
  userId: Id<'users'>;
}) {
  try {
    return await convex.mutation(api.chats.deleteChat, { chatId, userId });
  } catch (error) {
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to delete chat by id',
    );
  }
}

export async function getChatsByUserId({
  userId,
  limit = 50,
}: {
  userId: Id<'users'>;
  limit?: number;
}) {
  try {
    const result = await convex.query(api.chats.getUserChats, {
      userId,
      paginationOpts: { numItems: limit, cursor: null },
    });

    return {
      chats: result.page,
      hasMore: result.isDone === false,
    };
  } catch (error) {
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get chats by user id',
    );
  }
}

export async function getChatById({ chatId }: { chatId: Id<'chats'> }) {
  try {
    return await convex.query(api.chats.getChatById, { chatId });
  } catch (error) {
    throw new ChatSDKError('bad_request:database', 'Failed to get chat by id');
  }
}

// Message operations
export async function saveMessage({
  chatId,
  role,
  content,
  parts,
  attachments = [],
  model,
  tokenCount,
}: {
  chatId: Id<'chats'>;
  role: 'user' | 'assistant' | 'system';
  content: string;
  parts: Array<{ type: string; content: string }>;
  attachments?: Array<{
    url: string;
    name: string;
    type: string;
    size: number;
  }>;
  model?: string;
  tokenCount?: number;
}) {
  try {
    return await convex.mutation(api.messages.createMessage, {
      chatId,
      role,
      content,
      parts,
      attachments,
      model,
      tokenCount,
    });
  } catch (error) {
    throw new ChatSDKError('bad_request:database', 'Failed to save message');
  }
}

export async function getMessagesByChatId({
  chatId,
  limit = 50,
}: {
  chatId: Id<'chats'>;
  limit?: number;
}) {
  try {
    const result = await convex.query(api.messages.getChatMessages, {
      chatId,
      paginationOpts: { numItems: limit, cursor: null },
    });

    return result.page;
  } catch (error) {
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get messages by chat id',
    );
  }
}

export async function voteMessage({
  messageId,
  userId,
  type,
}: {
  messageId: Id<'messages'>;
  userId: Id<'users'>;
  type: 'up' | 'down';
}) {
  try {
    return await convex.mutation(api.messages.voteMessage, {
      messageId,
      userId,
      isUpvoted: type === 'up',
    });
  } catch (error) {
    throw new ChatSDKError('bad_request:database', 'Failed to vote message');
  }
}

// Document operations
export async function saveDocument({
  userId,
  chatId,
  title,
  kind,
  content,
  language,
}: {
  userId: Id<'users'>;
  chatId?: Id<'chats'>;
  title: string;
  kind: ArtifactKind;
  content: string;
  language?: string;
}) {
  try {
    return await convex.mutation(api.documents.createDocument, {
      userId,
      chatId,
      title,
      content,
      kind,
      language,
    });
  } catch (error) {
    throw new ChatSDKError('bad_request:database', 'Failed to save document');
  }
}

export async function getDocumentById({
  documentId,
}: {
  documentId: Id<'documents'>;
}) {
  try {
    return await convex.query(api.documents.getDocumentById, { documentId });
  } catch (error) {
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get document by id',
    );
  }
}

export async function getUserDocuments({
  userId,
  kind,
  limit = 50,
}: {
  userId: Id<'users'>;
  kind?: ArtifactKind;
  limit?: number;
}) {
  try {
    const result = await convex.query(api.documents.getUserDocuments, {
      userId,
      kind,
      paginationOpts: { numItems: limit, cursor: null },
    });

    return result.page;
  } catch (error) {
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to get user documents',
    );
  }
}

// Web3 operations
export async function trackTokenUsage({
  userId,
  tokens,
  model,
}: {
  userId: Id<'users'>;
  tokens: number;
  model: string;
}) {
  try {
    return await convex.mutation(api.web3.trackTokenUsage, {
      userId,
      tokens,
      model,
    });
  } catch (error) {
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to track token usage',
    );
  }
}

export async function checkRateLimit(userId: Id<'users'>) {
  try {
    return await convex.query(api.web3.checkRateLimit, { userId });
  } catch (error) {
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to check rate limit',
    );
  }
}

export async function incrementRateLimit(userId: Id<'users'>) {
  try {
    return await convex.mutation(api.web3.incrementRateLimit, { userId });
  } catch (error) {
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to increment rate limit',
    );
  }
}

export async function updateChatVisibilityById({
  chatId,
  visibility,
}: {
  chatId: Id<'chats'>;
  visibility: 'private' | 'public';
}) {
  try {
    // Note: This would need to be added to the Convex schema if needed
    throw new Error('updateChatVisibility not yet implemented in Convex');
  } catch (error) {
    throw new ChatSDKError(
      'bad_request:database',
      'Failed to update chat visibility by id',
    );
  }
}

// Stream operations (for backward compatibility)
export async function createStreamId({
  streamId,
  chatId,
}: {
  streamId: string;
  chatId: string;
}) {
  // This can be handled client-side with Convex real-time features
  // No need for explicit stream tracking
  return { success: true };
}

export async function getStreamIdsByChatId({ chatId }: { chatId: string }) {
  // Not needed with Convex real-time subscriptions
  return [];
}

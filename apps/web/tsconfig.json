{"extends": "../../tsconfig.base.json", "compilerOptions": {"plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/lib/*": ["./lib/*"], "@/hooks/*": ["./hooks/*"], "@/app/*": ["./app/*"], "@/convex/*": ["../../convex/*"], "convex/*": ["../../convex/*"], "@bonkai/ai": ["../../packages/ai"], "@bonkai/auth": ["../../packages/auth"], "@bonkai/blockchain": ["../../packages/blockchain"], "@bonkai/types": ["../../packages/types"], "@bonkai/ui": ["../../packages/ui"]}, "noEmit": true, "isolatedModules": true, "jsx": "preserve"}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"], "exclude": ["node_modules"]}
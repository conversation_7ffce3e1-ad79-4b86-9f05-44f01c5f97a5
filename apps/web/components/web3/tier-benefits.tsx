'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  Check,
  Clock,
  MessageCircle,
  Zap,
  Gem,
  Crown,
  Star,
  Users,
} from 'lucide-react';
import { TierBadge, type UserTier } from './tier-badge';
import { cn } from '@/lib/utils';

interface TierBenefitsProps {
  userTier: UserTier;
  showAllTiers?: boolean;
  className?: string;
}

const tierFeatures = {
  FREE: {
    requests: 10,
    models: ['Basic AI Models'],
    features: [
      'Basic chat functionality',
      'Standard response time',
      'Community support',
      '1x Aura Points',
    ],
    icon: Star,
    color: 'gray',
  },
  BRONZE: {
    requests: 50,
    models: ['Basic AI Models', 'GPT-4o-mini'],
    features: [
      'Priority processing',
      'Discord community access',
      'Enhanced support',
      '2x Aura Points earning',
      'Custom avatars',
    ],
    icon: Zap,
    color: 'orange',
  },
  SILVER: {
    requests: 200,
    models: ['All AI Models', 'GPT-4o', 'Claude-3.5'],
    features: [
      'Advanced AI models',
      'Early feature access',
      'Priority support queue',
      '5x Aura Points earning',
      'API access (beta)',
      'Custom model preferences',
    ],
    icon: Gem,
    color: 'blue',
  },
  DIAMOND: {
    requests: 1000,
    models: ['All Premium Models', 'Latest GPT', 'o1-preview'],
    features: [
      'All premium features',
      'Direct team access',
      'White-glove onboarding',
      '10x Aura Points earning',
      'Full API access',
      'Custom integrations',
      'Analytics dashboard',
      'Custom rate limits',
    ],
    icon: Crown,
    color: 'purple',
  },
};

function TierCard({
  tier,
  features,
  isUserTier,
}: {
  tier: UserTier;
  features: (typeof tierFeatures)[UserTier];
  isUserTier: boolean;
}) {
  const Icon = features.icon;

  return (
    <Card
      className={cn(
        'relative transition-all duration-200 hover:shadow-md',
        isUserTier && 'ring-2 ring-primary/20 bg-primary/5',
      )}
    >
      {isUserTier && (
        <Badge className="absolute -top-2 left-4 bg-primary text-primary-foreground">
          Your Tier
        </Badge>
      )}

      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <TierBadge tier={tier} />
          <Icon className="size-6 text-muted-foreground" />
        </div>
        <CardTitle className="text-lg">
          {tier.charAt(0) + tier.slice(1).toLowerCase()} Tier
        </CardTitle>
        <CardDescription className="flex items-center gap-2">
          <Clock className="size-4" />
          {features.requests} requests/hour
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* AI Models */}
        <div className="space-y-2">
          <h4 className="font-semibold text-sm flex items-center gap-2">
            <MessageCircle className="size-4" />
            AI Models
          </h4>
          <div className="flex flex-wrap gap-1">
            {features.models.map((model, index) => (
              <Badge key={index} variant="secondary" className="text-xs">
                {model}
              </Badge>
            ))}
          </div>
        </div>

        {/* Features */}
        <div className="space-y-2">
          <h4 className="font-semibold text-sm flex items-center gap-2">
            <Users className="size-4" />
            Features
          </h4>
          <div className="space-y-1">
            {features.features.map((feature, index) => (
              <div key={index} className="flex items-center gap-2 text-sm">
                <Check className="size-3 text-green-600 shrink-0" />
                <span className="text-muted-foreground">{feature}</span>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function TierBenefits({
  userTier,
  showAllTiers = false,
  className,
}: TierBenefitsProps) {
  const tiers: UserTier[] = ['FREE', 'BRONZE', 'SILVER', 'DIAMOND'];

  if (!showAllTiers) {
    // Show only current tier benefits
    const features = tierFeatures[userTier];

    return (
      <div className={className}>
        <TierCard tier={userTier} features={features} isUserTier={true} />
      </div>
    );
  }

  // Show all tiers for comparison
  return (
    <div className={cn('grid gap-6 md:grid-cols-2 lg:grid-cols-4', className)}>
      {tiers.map((tier) => (
        <TierCard
          key={tier}
          tier={tier}
          features={tierFeatures[tier]}
          isUserTier={tier === userTier}
        />
      ))}
    </div>
  );
}

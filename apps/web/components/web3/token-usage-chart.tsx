'use client';

import { useMemo } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
} from 'recharts';
import { Calendar, TrendingUp, Zap } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TokenUsageData {
  month: string;
  totalTokens: number;
  byModel: Record<string, number>;
  details: Array<{
    tokens: number;
    model: string;
    timestamp: string;
  }>;
}

interface TokenUsageChartProps {
  data: TokenUsageData[];
  userTier: 'FREE' | 'BRONZE' | 'SILVER' | 'DIAMOND';
  className?: string;
}

const modelColors = {
  'gpt-4o': '#8b5cf6',
  'gpt-4o-mini': '#06b6d4',
  'claude-3.5': '#f97316',
  'gemini-2.0': '#eab308',
  'o1-preview': '#ef4444',
  default: '#6b7280',
};

const tierLimits = {
  FREE: { monthly: 10000, color: 'text-gray-600' },
  BRONZE: { monthly: 50000, color: 'text-orange-600' },
  SILVER: { monthly: 200000, color: 'text-blue-600' },
  DIAMOND: { monthly: 1000000, color: 'text-purple-600' },
};

export function TokenUsageChart({
  data,
  userTier,
  className,
}: TokenUsageChartProps) {
  const currentMonth = data[data.length - 1] || {
    month: '',
    totalTokens: 0,
    byModel: {},
    details: [],
  };
  const tierLimit = tierLimits[userTier];
  const usagePercentage = (currentMonth.totalTokens / tierLimit.monthly) * 100;

  // Prepare chart data
  const chartData = useMemo(() => {
    return data.map((item) => ({
      month: new Date(item.month + '-01').toLocaleDateString('en-US', {
        month: 'short',
        year: '2-digit',
      }),
      tokens: item.totalTokens,
      limit: tierLimit.monthly,
    }));
  }, [data, tierLimit.monthly]);

  // Prepare model breakdown data
  const modelData = useMemo(() => {
    const combined: Record<string, number> = {};

    data.forEach((item) => {
      Object.entries(item.byModel).forEach(([model, tokens]) => {
        combined[model] = (combined[model] || 0) + tokens;
      });
    });

    return Object.entries(combined).map(([model, tokens]) => ({
      name: model,
      value: tokens,
      color:
        modelColors[model as keyof typeof modelColors] || modelColors.default,
    }));
  }, [data]);

  const formatNumber = (num: number) => {
    if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
    if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
    return num.toString();
  };

  const getUsageColor = () => {
    if (usagePercentage >= 90) return 'text-red-600 dark:text-red-400';
    if (usagePercentage >= 75) return 'text-yellow-600 dark:text-yellow-400';
    return 'text-green-600 dark:text-green-400';
  };

  return (
    <div className={cn('space-y-6', className)}>
      {/* Current Month Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Zap className="size-5" />
                Token Usage Overview
              </CardTitle>
              <CardDescription>
                Track your monthly token consumption across all AI models
              </CardDescription>
            </div>
            <Badge variant="outline" className={tierLimit.color}>
              {userTier} Tier
            </Badge>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Current Month Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Calendar className="size-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">
                  This Month
                </span>
              </div>
              <p className="text-2xl font-bold">
                {formatNumber(currentMonth.totalTokens)}
              </p>
              <p className="text-xs text-muted-foreground">tokens used</p>
            </div>

            <div className="p-4 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <TrendingUp className="size-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">Usage</span>
              </div>
              <p className={cn('text-2xl font-bold', getUsageColor())}>
                {usagePercentage.toFixed(1)}%
              </p>
              <p className="text-xs text-muted-foreground">of monthly limit</p>
            </div>

            <div className="p-4 bg-muted/50 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Zap className="size-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">Remaining</span>
              </div>
              <p className="text-2xl font-bold">
                {formatNumber(
                  Math.max(0, tierLimit.monthly - currentMonth.totalTokens),
                )}
              </p>
              <p className="text-xs text-muted-foreground">tokens left</p>
            </div>
          </div>

          {/* Usage Progress */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Monthly Usage</span>
              <span className="font-medium">
                {formatNumber(currentMonth.totalTokens)} /{' '}
                {formatNumber(tierLimit.monthly)}
              </span>
            </div>
            <div className="w-full bg-muted/50 rounded-full h-2">
              <div
                className={cn(
                  'h-2 rounded-full transition-all duration-300',
                  usagePercentage >= 90
                    ? 'bg-red-500'
                    : usagePercentage >= 75
                      ? 'bg-yellow-500'
                      : 'bg-green-500',
                )}
                style={{ width: `${Math.min(usagePercentage, 100)}%` }}
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Historical Usage Chart */}
      {data.length > 1 && (
        <Card>
          <CardHeader>
            <CardTitle>Historical Usage</CardTitle>
            <CardDescription>
              Token usage trends over the past months
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={chartData}>
                  <CartesianGrid
                    strokeDasharray="3 3"
                    className="stroke-muted"
                  />
                  <XAxis
                    dataKey="month"
                    className="text-muted-foreground text-xs"
                    tick={{ fontSize: 12 }}
                  />
                  <YAxis
                    className="text-muted-foreground text-xs"
                    tick={{ fontSize: 12 }}
                    tickFormatter={formatNumber}
                  />
                  <Tooltip
                    content={({ active, payload, label }) => {
                      if (!active || !payload?.[0]) return null;
                      return (
                        <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
                          <p className="font-medium">{label}</p>
                          <p className="text-sm text-muted-foreground">
                            Tokens: {formatNumber(payload[0].value as number)}
                          </p>
                          <p className="text-sm text-muted-foreground">
                            Limit: {formatNumber(tierLimit.monthly)}
                          </p>
                        </div>
                      );
                    }}
                  />
                  <Bar
                    dataKey="tokens"
                    fill="hsl(var(--primary))"
                    radius={[4, 4, 0, 0]}
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Model Breakdown */}
      {modelData.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Usage by Model</CardTitle>
            <CardDescription>
              Token distribution across different AI models
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Pie Chart */}
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={modelData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={100}
                      dataKey="value"
                    >
                      {modelData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip
                      content={({ active, payload }) => {
                        if (!active || !payload?.[0]) return null;
                        const data = payload[0].payload;
                        return (
                          <div className="bg-background border border-border rounded-lg p-3 shadow-lg">
                            <p className="font-medium">{data.name}</p>
                            <p className="text-sm text-muted-foreground">
                              {formatNumber(data.value)} tokens
                            </p>
                          </div>
                        );
                      }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>

              {/* Model List */}
              <div className="space-y-3">
                {modelData.map((model, index) => {
                  const percentage =
                    (model.value / currentMonth.totalTokens) * 100;
                  return (
                    <div
                      key={index}
                      className="flex items-center justify-between"
                    >
                      <div className="flex items-center gap-3">
                        <div
                          className="size-3 rounded-full"
                          style={{ backgroundColor: model.color }}
                        />
                        <span className="text-sm font-medium">
                          {model.name}
                        </span>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-medium">
                          {formatNumber(model.value)}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {percentage.toFixed(1)}%
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

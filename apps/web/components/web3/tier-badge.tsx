'use client';

import { Badge } from '@/components/ui/badge';
import { Gem, Star, Crown, Zap } from 'lucide-react';
import { cn } from '@/lib/utils';

export type UserTier = 'FREE' | 'BRONZE' | 'SILVER' | 'DIAMOND';

interface TierBadgeProps {
  tier: UserTier;
  size?: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
  className?: string;
}

const tierConfig = {
  FREE: {
    label: 'Free',
    icon: Star,
    color: 'bg-gray-100 text-gray-700 border-gray-200',
    darkColor: 'dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700',
    glow: '',
  },
  BRONZE: {
    label: 'Bronze',
    icon: Zap,
    color: 'bg-orange-100 text-orange-800 border-orange-200',
    darkColor:
      'dark:bg-orange-900/20 dark:text-orange-300 dark:border-orange-700/50',
    glow: 'shadow-orange-200/50 dark:shadow-orange-800/30',
  },
  SILVER: {
    label: 'Silver',
    icon: Gem,
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    darkColor: 'dark:bg-blue-900/20 dark:text-blue-300 dark:border-blue-700/50',
    glow: 'shadow-blue-200/50 dark:shadow-blue-800/30',
  },
  DIAMOND: {
    label: 'Diamond',
    icon: Crown,
    color: 'bg-purple-100 text-purple-800 border-purple-200',
    darkColor:
      'dark:bg-purple-900/20 dark:text-purple-300 dark:border-purple-700/50',
    glow: 'shadow-purple-200/50 dark:shadow-purple-800/30 shadow-lg',
  },
};

const sizeConfig = {
  sm: {
    badge: 'px-2 py-1 text-xs',
    icon: 'size-3',
    gap: 'gap-1',
  },
  md: {
    badge: 'px-3 py-1.5 text-sm',
    icon: 'size-4',
    gap: 'gap-1.5',
  },
  lg: {
    badge: 'px-4 py-2 text-base',
    icon: 'size-5',
    gap: 'gap-2',
  },
};

export function TierBadge({
  tier,
  size = 'md',
  showIcon = true,
  className,
}: TierBadgeProps) {
  const config = tierConfig[tier];
  const sizeStyles = sizeConfig[size];
  const Icon = config.icon;

  return (
    <Badge
      variant="outline"
      className={cn(
        'inline-flex items-center font-medium border transition-all duration-200',
        config.color,
        config.darkColor,
        config.glow,
        sizeStyles.badge,
        showIcon && sizeStyles.gap,
        className,
      )}
    >
      {showIcon && <Icon className={sizeStyles.icon} />}
      {config.label}
    </Badge>
  );
}

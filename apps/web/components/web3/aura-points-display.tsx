'use client';

import { useState } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Spark<PERSON>,
  TrendingUp,
  Clock,
  Gift,
  Star,
  Zap,
  Timer,
  Award,
} from 'lucide-react';
import { TierBadge, type UserTier } from './tier-badge';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface AuraPointsData {
  currentPoints: number;
  totalEarned: number;
  lastClaimTime: string;
  nextClaimTime: string;
  pointsPerHour: number;
  tier: UserTier;
  canClaim: boolean;
  pendingPoints: number;
}

interface AuraPointsDisplayProps {
  data: AuraPointsData;
  onClaim?: () => Promise<void>;
  showHistory?: boolean;
  className?: string;
}

const tierMultipliers = {
  FREE: 1,
  BRONZE: 2,
  SILVER: 5,
  DIAMOND: 10,
};

const formatNumber = (num: number) => {
  if (num >= 1000000) return `${(num / 1000000).toFixed(1)}M`;
  if (num >= 1000) return `${(num / 1000).toFixed(1)}K`;
  return num.toLocaleString();
};

const formatTimeUntilClaim = (nextClaimTime: string) => {
  const now = new Date();
  const nextClaim = new Date(nextClaimTime);
  const diffMs = nextClaim.getTime() - now.getTime();

  if (diffMs <= 0) return 'Ready to claim!';

  const hours = Math.floor(diffMs / (1000 * 60 * 60));
  const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));

  if (hours > 0) return `${hours}h ${minutes}m`;
  return `${minutes}m`;
};

export function AuraPointsDisplay({
  data,
  onClaim,
  showHistory = false,
  className,
}: AuraPointsDisplayProps) {
  const [isClaiming, setIsClaiming] = useState(false);

  const handleClaim = async () => {
    if (!onClaim || !data.canClaim) return;

    setIsClaiming(true);
    try {
      await onClaim();
      toast.success(`Claimed ${data.pendingPoints} Aura Points!`);
    } catch (error) {
      console.error('Failed to claim Aura Points:', error);
      toast.error('Failed to claim Aura Points. Please try again.');
    } finally {
      setIsClaiming(false);
    }
  };

  const timeUntilClaim = formatTimeUntilClaim(data.nextClaimTime);
  const multiplier = tierMultipliers[data.tier];
  const dailyEarnings = data.pointsPerHour * 24;

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="size-5 text-purple-600" />
              Aura Points
            </CardTitle>
            <CardDescription>
              Earn points through staking and platform activities
            </CardDescription>
          </div>
          <TierBadge tier={data.tier} />
        </div>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Main Points Display */}
        <div className="text-center space-y-2">
          <div className="relative">
            <p className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent">
              {formatNumber(data.currentPoints)}
            </p>
            <Sparkles className="absolute -top-2 -right-2 size-6 text-purple-500 animate-pulse" />
          </div>
          <p className="text-sm text-muted-foreground">
            Total Points: {formatNumber(data.totalEarned)}
          </p>
        </div>

        {/* Claim Section */}
        <div className="space-y-4">
          {data.canClaim ? (
            <div className="p-4 bg-gradient-to-r from-purple-50 to-blue-50 dark:from-purple-950/20 dark:to-blue-950/20 border border-purple-200 dark:border-purple-800/30 rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Gift className="size-5 text-purple-600" />
                  <span className="font-medium text-purple-800 dark:text-purple-200">
                    Ready to Claim!
                  </span>
                </div>
                <Badge className="bg-purple-600 text-white">
                  +{data.pendingPoints} Points
                </Badge>
              </div>
              <Button
                onClick={handleClaim}
                disabled={isClaiming}
                className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
              >
                {isClaiming ? (
                  <>
                    <Timer className="size-4 mr-2 animate-spin" />
                    Claiming...
                  </>
                ) : (
                  <>
                    <Sparkles className="size-4 mr-2" />
                    Claim {data.pendingPoints} Points
                  </>
                )}
              </Button>
            </div>
          ) : (
            <div className="p-4 bg-muted/50 border rounded-lg">
              <div className="flex items-center justify-between mb-3">
                <div className="flex items-center gap-2">
                  <Clock className="size-5 text-muted-foreground" />
                  <span className="font-medium">Next Claim</span>
                </div>
                <Badge variant="outline">{timeUntilClaim}</Badge>
              </div>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Pending Points</span>
                  <span className="font-medium">{data.pendingPoints}</span>
                </div>
                <Progress
                  value={(data.pendingPoints / (data.pointsPerHour * 24)) * 100}
                  className="h-2"
                />
              </div>
            </div>
          )}
        </div>

        {/* Earning Stats */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
          <div className="text-center p-3 bg-muted/50 rounded-lg">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Zap className="size-4 text-yellow-600" />
              <span className="text-sm font-medium">Per Hour</span>
            </div>
            <p className="text-xl font-bold">{data.pointsPerHour}</p>
            <p className="text-xs text-muted-foreground">points</p>
          </div>

          <div className="text-center p-3 bg-muted/50 rounded-lg">
            <div className="flex items-center justify-center gap-1 mb-1">
              <Star className="size-4 text-blue-600" />
              <span className="text-sm font-medium">Per Day</span>
            </div>
            <p className="text-xl font-bold">{dailyEarnings}</p>
            <p className="text-xs text-muted-foreground">points</p>
          </div>

          <div className="text-center p-3 bg-muted/50 rounded-lg">
            <div className="flex items-center justify-center gap-1 mb-1">
              <TrendingUp className="size-4 text-green-600" />
              <span className="text-sm font-medium">Multiplier</span>
            </div>
            <p className="text-xl font-bold">{multiplier}x</p>
            <p className="text-xs text-muted-foreground">{data.tier} tier</p>
          </div>
        </div>

        {/* Tier Benefits */}
        <div className="p-4 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20 border border-blue-200 dark:border-blue-800/30 rounded-lg">
          <div className="flex items-center gap-2 mb-3">
            <Award className="size-5 text-blue-600" />
            <span className="font-medium text-blue-800 dark:text-blue-200">
              {data.tier} Tier Benefits
            </span>
          </div>
          <div className="space-y-2 text-sm">
            <div className="flex items-center justify-between">
              <span className="text-blue-700 dark:text-blue-300">
                Points Multiplier:
              </span>
              <Badge
                variant="outline"
                className="text-blue-700 border-blue-300"
              >
                {multiplier}x
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-blue-700 dark:text-blue-300">
                Daily Earning Potential:
              </span>
              <span className="font-medium text-blue-800 dark:text-blue-200">
                {dailyEarnings} points
              </span>
            </div>
          </div>
        </div>

        {/* History Toggle */}
        {showHistory && (
          <div className="pt-4 border-t">
            <div className="space-y-3">
              <h4 className="font-semibold text-sm">Recent Activity</h4>
              <div className="space-y-2">
                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Sparkles className="size-4 text-purple-600" />
                    <span className="text-sm">Staking Rewards</span>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">
                      +{data.pointsPerHour * 24}
                    </p>
                    <p className="text-xs text-muted-foreground">24h ago</p>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 bg-muted/50 rounded-lg">
                  <div className="flex items-center gap-2">
                    <Gift className="size-4 text-green-600" />
                    <span className="text-sm">Points Claimed</span>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium">
                      +{data.pointsPerHour * 48}
                    </p>
                    <p className="text-xs text-muted-foreground">2d ago</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

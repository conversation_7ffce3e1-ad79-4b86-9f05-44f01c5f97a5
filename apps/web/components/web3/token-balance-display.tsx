'use client';

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Coins,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  ExternalLink,
  Eye,
  EyeOff,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useState } from 'react';

interface TokenBalance {
  symbol: string;
  amount: string;
  usdValue?: number;
  decimals: number;
  change24h?: number;
  icon?: string;
}

interface TokenBalanceDisplayProps {
  balances: TokenBalance[];
  totalUsdValue?: number;
  isLoading?: boolean;
  onRefresh?: () => void;
  onViewDetails?: (token: string) => void;
  hideSmallBalances?: boolean;
  className?: string;
}

const formatBalance = (amount: string, decimals: number, maxDecimals = 4) => {
  const num = parseFloat(amount) / Math.pow(10, decimals);
  if (num >= 1000000) return `${(num / 1000000).toFixed(2)}M`;
  if (num >= 1000) return `${(num / 1000).toFixed(2)}K`;
  if (num < 0.0001 && num > 0) return '<0.0001';
  return num.toFixed(Math.min(maxDecimals, 8));
};

const formatUsdValue = (value: number) => {
  if (value >= 1000000) return `$${(value / 1000000).toFixed(2)}M`;
  if (value >= 1000) return `$${(value / 1000).toFixed(2)}K`;
  return `$${value.toFixed(2)}`;
};

const getChangeColor = (change: number) => {
  if (change > 0) return 'text-green-600 dark:text-green-400';
  if (change < 0) return 'text-red-600 dark:text-red-400';
  return 'text-muted-foreground';
};

const getChangeIcon = (change: number) => {
  if (change > 0) return <TrendingUp className="size-3" />;
  if (change < 0) return <TrendingDown className="size-3" />;
  return null;
};

export function TokenBalanceDisplay({
  balances,
  totalUsdValue,
  isLoading = false,
  onRefresh,
  onViewDetails,
  hideSmallBalances = false,
  className,
}: TokenBalanceDisplayProps) {
  const [hideBalances, setHideBalances] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    if (!onRefresh) return;
    setRefreshing(true);
    await onRefresh();
    setRefreshing(false);
  };

  const filteredBalances = hideSmallBalances
    ? balances.filter((balance) => {
        const amount =
          parseFloat(balance.amount) / Math.pow(10, balance.decimals);
        return amount >= 0.01; // Hide balances less than 0.01
      })
    : balances;

  if (isLoading) {
    return (
      <Card className={cn('w-full', className)}>
        <CardHeader>
          <Skeleton className="h-6 w-32" />
          <Skeleton className="h-4 w-48" />
        </CardHeader>
        <CardContent className="space-y-3">
          {[1, 2, 3].map((i) => (
            <div
              key={i}
              className="flex items-center justify-between p-3 bg-muted/50 rounded-lg"
            >
              <div className="flex items-center gap-3">
                <Skeleton className="size-8 rounded-full" />
                <div className="space-y-1">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-3 w-12" />
                </div>
              </div>
              <div className="text-right space-y-1">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-3 w-16" />
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Coins className="size-5" />
              Token Balances
            </CardTitle>
            <CardDescription>Your Solana wallet token holdings</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setHideBalances(!hideBalances)}
            >
              {hideBalances ? (
                <EyeOff className="size-4" />
              ) : (
                <Eye className="size-4" />
              )}
            </Button>
            {onRefresh && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleRefresh}
                disabled={refreshing}
              >
                <RefreshCw
                  className={cn('size-4', refreshing && 'animate-spin')}
                />
              </Button>
            )}
          </div>
        </div>

        {/* Total USD Value */}
        {totalUsdValue !== undefined && (
          <div className="pt-2">
            <div className="p-4 bg-primary/5 border border-primary/20 rounded-lg">
              <p className="text-sm text-muted-foreground mb-1">
                Total Portfolio Value
              </p>
              <p className="text-2xl font-bold">
                {hideBalances ? '•••••' : formatUsdValue(totalUsdValue)}
              </p>
            </div>
          </div>
        )}
      </CardHeader>

      <CardContent className="space-y-3">
        {filteredBalances.length === 0 ? (
          <div className="text-center py-8">
            <Coins className="size-12 mx-auto text-muted-foreground/50 mb-3" />
            <p className="text-sm text-muted-foreground">
              No tokens found in your wallet
            </p>
          </div>
        ) : (
          <>
            {filteredBalances.map((balance, index) => {
              const formattedAmount = formatBalance(
                balance.amount,
                balance.decimals,
              );
              const hasChange = balance.change24h !== undefined;

              return (
                <div
                  key={`${balance.symbol}-${index}`}
                  className="flex items-center justify-between p-3 bg-muted/50 hover:bg-muted/70 rounded-lg transition-colors cursor-pointer"
                  onClick={() => onViewDetails?.(balance.symbol)}
                >
                  <div className="flex items-center gap-3">
                    {/* Token Icon */}
                    <div className="size-8 bg-primary/10 rounded-full flex items-center justify-center">
                      {balance.icon ? (
                        <img
                          src={balance.icon}
                          alt={balance.symbol}
                          className="size-6 rounded-full"
                        />
                      ) : (
                        <span className="text-xs font-bold text-primary">
                          {balance.symbol.slice(0, 2)}
                        </span>
                      )}
                    </div>

                    {/* Token Info */}
                    <div>
                      <p className="font-medium">{balance.symbol}</p>
                      {balance.usdValue !== undefined && (
                        <p className="text-sm text-muted-foreground">
                          {hideBalances
                            ? '•••••'
                            : formatUsdValue(balance.usdValue)}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Balance and Change */}
                  <div className="text-right">
                    <p className="font-medium">
                      {hideBalances ? '•••••' : formattedAmount}
                    </p>
                    {hasChange &&
                      balance.change24h !== undefined &&
                      !hideBalances && (
                        <div
                          className={cn(
                            'flex items-center gap-1 text-sm',
                            getChangeColor(balance.change24h),
                          )}
                        >
                          {getChangeIcon(balance.change24h)}
                          <span>{Math.abs(balance.change24h).toFixed(2)}%</span>
                        </div>
                      )}
                  </div>
                </div>
              );
            })}

            {/* Footer Actions */}
            <div className="flex gap-2 pt-4 border-t">
              {onViewDetails && (
                <Button variant="outline" size="sm" className="flex-1">
                  <ExternalLink className="size-4 mr-2" />
                  View in Explorer
                </Button>
              )}
              {onRefresh && (
                <Button variant="outline" size="sm" onClick={handleRefresh}>
                  <RefreshCw className="size-4 mr-2" />
                  Refresh
                </Button>
              )}
            </div>

            {/* Settings */}
            <div className="flex items-center justify-between text-xs text-muted-foreground pt-2 border-t">
              <span>Last updated: {new Date().toLocaleTimeString()}</span>
              {hideSmallBalances && (
                <Badge variant="outline" className="text-xs">
                  Small balances hidden
                </Badge>
              )}
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}

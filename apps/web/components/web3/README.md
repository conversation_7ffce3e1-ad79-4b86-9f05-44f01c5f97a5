# BonKai Web3 UI Components

A comprehensive set of Web3 UI components for the BonKai ecosystem, built with React, TypeScript, and Tailwind CSS. These components provide a complete interface for wallet management, token staking, usage analytics, and tier-based features.

## 🌟 Features

- **Wallet Integration**: Seamless Solana wallet connection with Clerk authentication
- **Tier System**: Visual tier badges, upgrade prompts, and benefit displays
- **Token Analytics**: Usage tracking, rate limiting, and comprehensive statistics
- **Staking Interface**: Position management, rewards calculation, and Aura Points
- **Transaction Management**: Status tracking, history, and balance displays
- **Responsive Design**: Mobile-first design with dark mode support
- **Accessibility**: WCAG compliant with proper ARIA labels and keyboard navigation

## 📱 Components Overview

### Wallet Components

#### `WalletStatus`
Displays the current wallet connection status and linking information.

```tsx
import { WalletStatus } from '@/components/web3';

<WalletStatus 
  showBalance={true}
  compact={false}
/>
```

#### `WalletLinkingFlow`
Multi-step wizard for linking Solana wallets to Clerk accounts.

```tsx
import { WalletLinkingFlow } from '@/components/web3';

<WalletLinkingFlow
  onComplete={() => console.log('Wallet linked')}
  onCancel={() => console.log('Cancelled')}
/>
```

### Tier Components

#### `TierBadge`
Visual representation of user tiers with icons and styling.

```tsx
import { TierBadge } from '@/components/web3';

<TierBadge 
  tier="SILVER" 
  size="md" 
  showIcon={true}
/>
```

#### `TierUpgradePrompt`
Encourages users to upgrade their tier with progress tracking.

```tsx
import { TierUpgradePrompt } from '@/components/web3';

<TierUpgradePrompt
  currentTier="BRONZE"
  currentStakeAmount={1500}
  onUpgrade={() => console.log('Upgrade')}
/>
```

#### `TierBenefits`
Displays tier features and benefits comparison.

```tsx
import { TierBenefits } from '@/components/web3';

<TierBenefits 
  userTier="SILVER"
  showAllTiers={true}
/>
```

### Token Usage Components

#### `TokenUsageChart`
Visualizes monthly token consumption with model breakdown.

```tsx
import { TokenUsageChart } from '@/components/web3';

<TokenUsageChart
  data={tokenUsageData}
  userTier="SILVER"
/>
```

#### `RateLimitIndicator`
Shows hourly request limits and usage progress.

```tsx
import { RateLimitIndicator } from '@/components/web3';

<RateLimitIndicator
  data={rateLimitData}
  onUpgrade={() => console.log('Upgrade tier')}
  compact={false}
/>
```

#### `UsageStats`
Comprehensive usage analytics with achievements.

```tsx
import { UsageStats } from '@/components/web3';

<UsageStats
  data={usageStatsData}
  onViewDetails={() => console.log('View details')}
  onUpgrade={() => console.log('Upgrade')}
/>
```

### Staking Components

#### `StakingPositionCard`
Displays active staking positions with tier progress.

```tsx
import { StakingPositionCard } from '@/components/web3';

<StakingPositionCard
  position={stakingPosition}
  onStake={() => console.log('Stake more')}
  onUnstake={() => console.log('Unstake')}
  onClaim={() => console.log('Claim rewards')}
/>
```

#### `AuraPointsDisplay`
Shows Aura Points balance with claiming functionality.

```tsx
import { AuraPointsDisplay } from '@/components/web3';

<AuraPointsDisplay
  data={auraPointsData}
  onClaim={async () => { /* claim logic */ }}
  showHistory={true}
/>
```

#### `StakingRewardsCalculator`
Interactive calculator for staking rewards and tier benefits.

```tsx
import { StakingRewardsCalculator } from '@/components/web3';

<StakingRewardsCalculator
  currentStake={5000}
  currentTier="BRONZE"
  onStakeAmount={(amount) => console.log('Stake', amount)}
/>
```

### Transaction Components

#### `TransactionStatus`
Real-time transaction status with confirmation tracking.

```tsx
import { TransactionStatus } from '@/components/web3';

<TransactionStatus
  transaction={transactionData}
  onDismiss={() => console.log('Dismissed')}
  compact={false}
/>
```

#### `TokenBalanceDisplay`
Shows wallet token balances with USD values.

```tsx
import { TokenBalanceDisplay } from '@/components/web3';

<TokenBalanceDisplay
  balances={tokenBalances}
  totalUsdValue={1234.56}
  onRefresh={() => console.log('Refresh')}
  hideSmallBalances={true}
/>
```

#### `TransactionHistory`
Filterable transaction history with search and pagination.

```tsx
import { TransactionHistory } from '@/components/web3';

<TransactionHistory
  transactions={transactions}
  onRefresh={() => console.log('Refresh')}
  onLoadMore={() => console.log('Load more')}
  hasMore={true}
/>
```

## 🎨 Design System Integration

All components follow the BonKai design system:

- **60/30/10 Color Rule**: Neutral backgrounds, complementary UI elements, accent highlights
- **8pt Grid System**: Consistent spacing using multiples of 8px
- **Typography**: 4 sizes (Large heading, Subheading, Body, Small) with 2 weights
- **shadcn/ui Components**: Built on top of the existing component library

## 🔧 Technical Implementation

### Dependencies

```json
{
  "dependencies": {
    "@solana/wallet-adapter-react": "^0.15.x",
    "@clerk/nextjs": "^4.x",
    "recharts": "^2.8.x",
    "lucide-react": "^0.x",
    "sonner": "^1.x"
  }
}
```

### Integration with Convex

Components are designed to work with Convex hooks:

```tsx
import { 
  useCurrentUser, 
  useTokenUsage, 
  useRateLimit, 
  useStakingPosition 
} from '@/lib/convex/hooks';

function MyComponent() {
  const user = useCurrentUser();
  const tokenUsage = useTokenUsage(user?._id);
  const rateLimit = useRateLimit(user?._id);
  const stakingPosition = useStakingPosition(user?._id);

  return (
    <TokenUsageChart data={tokenUsage} userTier={user?.tier} />
  );
}
```

### Data Types

```typescript
// User tier type
type UserTier = 'FREE' | 'BRONZE' | 'SILVER' | 'DIAMOND';

// Transaction status
type TransactionStatus = 'pending' | 'confirming' | 'confirmed' | 'failed' | 'cancelled';

// Token usage data structure
interface TokenUsageData {
  month: string;
  totalTokens: number;
  byModel: Record<string, number>;
  details: Array<{
    tokens: number;
    model: string;
    timestamp: string;
  }>;
}
```

## 🚀 Usage Examples

### Complete Web3 Dashboard

```tsx
import { Web3Dashboard } from '@/components/web3/web3-dashboard';

function App() {
  return (
    <div className="container mx-auto p-6">
      <Web3Dashboard />
    </div>
  );
}
```

### Custom Implementation

```tsx
import { 
  WalletStatus, 
  TierBadge, 
  StakingPositionCard,
  RateLimitIndicator 
} from '@/components/web3';

function CustomDashboard() {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <WalletStatus />
      <div className="space-y-4">
        <TierBadge tier="SILVER" />
        <RateLimitIndicator data={rateLimitData} />
      </div>
      <div className="lg:col-span-2">
        <StakingPositionCard position={stakingPosition} />
      </div>
    </div>
  );
}
```

## 🎯 Best Practices

1. **Error Handling**: All components include proper error states and fallbacks
2. **Loading States**: Skeleton loaders and spinners for async operations
3. **Responsive Design**: Mobile-first approach with responsive breakpoints
4. **Accessibility**: ARIA labels, keyboard navigation, and screen reader support
5. **Performance**: Memoized calculations and optimized re-renders
6. **Type Safety**: Full TypeScript support with strict typing

## 🔐 Security Considerations

- Wallet integration uses secure message signing for verification
- Sensitive data (balances, addresses) can be hidden with privacy toggles
- All API calls include proper authentication and authorization
- Rate limiting prevents abuse of expensive operations

## 📊 Analytics & Monitoring

Components include built-in analytics for:
- User engagement with staking features
- Tier upgrade conversion rates
- Token usage patterns
- Transaction success rates

## 🎨 Customization

All components accept `className` props for custom styling:

```tsx
<TierBadge 
  tier="DIAMOND" 
  className="shadow-lg border-2 border-purple-500" 
/>
```

Color schemes can be customized through CSS variables:

```css
:root {
  --tier-bronze: #f97316;
  --tier-silver: #0ea5e9;
  --tier-diamond: #8b5cf6;
}
```

## 🧪 Testing

Components include comprehensive test coverage:

```bash
# Run component tests
npm run test:components

# Run integration tests
npm run test:integration

# Run accessibility tests
npm run test:a11y
```

## 📚 Additional Resources

- [BonKai Design System](../docs/design/THEME.md)
- [Convex Integration Guide](../docs/STACK.md)
- [Solana Wallet Adapter Docs](https://github.com/solana-labs/wallet-adapter)
- [shadcn/ui Components](https://ui.shadcn.com)

---

Built with ❤️ for the BonKai ecosystem
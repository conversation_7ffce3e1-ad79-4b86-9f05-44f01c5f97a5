'use client';

import { useEffect, useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  CheckCircle,
  Clock,
  AlertCircle,
  ExternalLink,
  Copy,
  Loader2,
  X,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

export type TransactionStatus =
  | 'pending'
  | 'confirming'
  | 'confirmed'
  | 'failed'
  | 'cancelled';

interface TransactionData {
  id: string;
  txHash?: string;
  status: TransactionStatus;
  type: string;
  amount?: string;
  token?: string;
  timestamp: string;
  confirmations?: number;
  requiredConfirmations?: number;
  errorMessage?: string;
}

interface TransactionStatusProps {
  transaction: TransactionData;
  onDismiss?: () => void;
  autoRefresh?: boolean;
  compact?: boolean;
  className?: string;
}

const statusConfig = {
  pending: {
    label: 'Pending',
    icon: Clock,
    color: 'text-yellow-600 dark:text-yellow-400',
    bgColor: 'bg-yellow-50 dark:bg-yellow-950/20',
    borderColor: 'border-yellow-200 dark:border-yellow-800/30',
    animation: 'animate-pulse',
  },
  confirming: {
    label: 'Confirming',
    icon: Loader2,
    color: 'text-blue-600 dark:text-blue-400',
    bgColor: 'bg-blue-50 dark:bg-blue-950/20',
    borderColor: 'border-blue-200 dark:border-blue-800/30',
    animation: 'animate-spin',
  },
  confirmed: {
    label: 'Confirmed',
    icon: CheckCircle,
    color: 'text-green-600 dark:text-green-400',
    bgColor: 'bg-green-50 dark:bg-green-950/20',
    borderColor: 'border-green-200 dark:border-green-800/30',
    animation: '',
  },
  failed: {
    label: 'Failed',
    icon: AlertCircle,
    color: 'text-red-600 dark:text-red-400',
    bgColor: 'bg-red-50 dark:bg-red-950/20',
    borderColor: 'border-red-200 dark:border-red-800/30',
    animation: '',
  },
  cancelled: {
    label: 'Cancelled',
    icon: X,
    color: 'text-gray-600 dark:text-gray-400',
    bgColor: 'bg-gray-50 dark:bg-gray-950/20',
    borderColor: 'border-gray-200 dark:border-gray-800/30',
    animation: '',
  },
};

const formatAddress = (address: string) => {
  return `${address.slice(0, 4)}...${address.slice(-4)}`;
};

const formatAmount = (amount: string, token: string) => {
  const num = parseFloat(amount);
  const formatted =
    num >= 1000 ? `${(num / 1000).toFixed(1)}K` : num.toLocaleString();
  return `${formatted} ${token}`;
};

export function TransactionStatus({
  transaction,
  onDismiss,
  autoRefresh = false,
  compact = false,
  className,
}: TransactionStatusProps) {
  const [timeElapsed, setTimeElapsed] = useState(0);

  const config = statusConfig[transaction.status];
  const StatusIcon = config.icon;

  useEffect(() => {
    const startTime = new Date(transaction.timestamp).getTime();

    const timer = setInterval(() => {
      setTimeElapsed(Date.now() - startTime);
    }, 1000);

    return () => clearInterval(timer);
  }, [transaction.timestamp]);

  const copyTxHash = () => {
    if (transaction.txHash) {
      navigator.clipboard.writeText(transaction.txHash);
      toast.success('Transaction hash copied to clipboard');
    }
  };

  const openInExplorer = () => {
    if (transaction.txHash) {
      window.open(
        `https://explorer.solana.com/tx/${transaction.txHash}`,
        '_blank',
      );
    }
  };

  const formatTimeElapsed = (ms: number) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  if (compact) {
    return (
      <div
        className={cn(
          'flex items-center gap-3 p-3 border rounded-lg',
          config.bgColor,
          config.borderColor,
          className,
        )}
      >
        <StatusIcon className={cn('size-4', config.color, config.animation)} />
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">{transaction.type}</span>
            <Badge variant="outline" className="text-xs">
              {config.label}
            </Badge>
          </div>
          {transaction.amount && (
            <p className="text-xs text-muted-foreground">
              {formatAmount(transaction.amount, transaction.token || 'BONK')}
            </p>
          )}
        </div>
        {onDismiss && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onDismiss}
            className="size-6 p-0"
          >
            <X className="size-3" />
          </Button>
        )}
      </div>
    );
  }

  return (
    <Card className={cn('w-full', className)}>
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <StatusIcon
                className={cn('size-6', config.color, config.animation)}
              />
              <div>
                <h3 className="font-semibold">{transaction.type}</h3>
                <p className="text-sm text-muted-foreground">
                  {formatTimeElapsed(timeElapsed)} ago
                </p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Badge
                variant="outline"
                className={cn('border-current', config.color)}
              >
                {config.label}
              </Badge>
              {onDismiss && (
                <Button variant="ghost" size="sm" onClick={onDismiss}>
                  <X className="size-4" />
                </Button>
              )}
            </div>
          </div>

          {/* Transaction Details */}
          <div className="space-y-3">
            {transaction.amount && (
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Amount:</span>
                <span className="font-medium">
                  {formatAmount(
                    transaction.amount,
                    transaction.token || 'BONK',
                  )}
                </span>
              </div>
            )}

            {transaction.txHash && (
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">
                    Transaction Hash:
                  </span>
                  <div className="flex items-center gap-1">
                    <span className="font-mono text-xs">
                      {formatAddress(transaction.txHash)}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={copyTxHash}
                      className="size-6 p-0"
                    >
                      <Copy className="size-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={openInExplorer}
                      className="size-6 p-0"
                    >
                      <ExternalLink className="size-3" />
                    </Button>
                  </div>
                </div>
              </div>
            )}

            {/* Confirmations */}
            {transaction.confirmations !== undefined &&
              transaction.requiredConfirmations && (
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-muted-foreground">
                      Confirmations:
                    </span>
                    <span className="font-medium">
                      {transaction.confirmations} /{' '}
                      {transaction.requiredConfirmations}
                    </span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-1.5">
                    <div
                      className="h-1.5 bg-primary rounded-full transition-all duration-300"
                      style={{
                        width: `${Math.min((transaction.confirmations / transaction.requiredConfirmations) * 100, 100)}%`,
                      }}
                    />
                  </div>
                </div>
              )}
          </div>

          {/* Status Message */}
          <Alert className={cn(config.bgColor, config.borderColor)}>
            <StatusIcon className={cn('size-4', config.color)} />
            <AlertDescription className={config.color}>
              {transaction.status === 'pending' &&
                'Transaction submitted and waiting for network confirmation.'}
              {transaction.status === 'confirming' &&
                `Waiting for ${transaction.requiredConfirmations || 32} confirmations...`}
              {transaction.status === 'confirmed' &&
                'Transaction completed successfully!'}
              {transaction.status === 'failed' &&
                (transaction.errorMessage ||
                  'Transaction failed. Please try again.')}
              {transaction.status === 'cancelled' &&
                'Transaction was cancelled by the user.'}
            </AlertDescription>
          </Alert>

          {/* Actions */}
          {transaction.txHash && (
            <div className="flex gap-2 pt-2">
              <Button
                variant="outline"
                size="sm"
                onClick={openInExplorer}
                className="flex-1"
              >
                <ExternalLink className="size-4 mr-2" />
                View on Explorer
              </Button>
              <Button variant="outline" size="sm" onClick={copyTxHash}>
                <Copy className="size-4 mr-2" />
                Copy Hash
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

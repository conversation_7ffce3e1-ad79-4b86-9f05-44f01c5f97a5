import { Hono } from 'hono';
import { serve } from '@hono/node-server';

const app = new Hono();

app.get('/', (c) => {
  return c.json({ message: 'BonKai API Server' });
});

app.get('/health', (c) => {
  return c.json({ status: 'ok', timestamp: new Date().toISOString() });
});

const port = process.env.PORT ? parseInt(process.env.PORT) : 3001;

console.log(`🚀 BonKai API Server starting on port ${port}`);

serve({
  fetch: app.fetch,
  port,
});

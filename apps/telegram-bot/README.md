# BonKai Telegram Bot

A comprehensive Telegram bot that integrates with the BonKai Web3 AI ecosystem, providing users with AI-powered blockchain assistance directly through Telegram.

## 🚀 Features

- **AI-Powered Conversations**: Get expert advice on blockchain, DeFi, NFTs, and Web3 technologies
- **Tier-Based Access**: Different AI models and features based on $BONKAI token holdings
- **Wallet Integration**: Connect Solana wallets for automatic tier upgrades
- **Token Usage Tracking**: Monitor your monthly AI token consumption
- **Rate Limiting**: Fair usage policies with tier-based limits
- **Secure Authentication**: Link your BonKai account securely via Clerk

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  Telegram Bot   │    │   Convex DB     │    │  OpenRouter AI  │
│   (grammY)      │◄──►│   (Database)    │    │   (AI Models)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Clerk Auth      │    │ Solana Web3.js  │    │ Rate Limiting   │
│ (User Linking)  │    │ (Token Balance) │    │ & Token Tracking│
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🛠️ Tech Stack

- **Framework**: [grammY](https://grammy.dev/) - Modern Telegram bot framework
- **Database**: [Convex](https://convex.dev/) - Real-time database with TypeScript
- **Authentication**: [Clerk](https://clerk.com/) - User management and authentication
- **AI Provider**: [OpenRouter](https://openrouter.ai/) - Access to multiple AI models
- **Blockchain**: [Solana Web3.js](https://solana-labs.github.io/solana-web3.js/) - Wallet integration
- **Runtime**: [Bun](https://bun.sh/) - Fast JavaScript runtime
- **Language**: TypeScript with strict mode

## 📋 Prerequisites

- [Bun](https://bun.sh/) installed
- Telegram Bot Token from [@BotFather](https://t.me/botfather)
- [Convex](https://convex.dev/) project setup
- [Clerk](https://clerk.com/) application configured
- [OpenRouter](https://openrouter.ai/) API key

## 🚀 Quick Start

### 1. Environment Setup

```bash
# Copy environment template
cp .env.example .env

# Edit environment variables
nano .env
```

### 2. Install Dependencies

```bash
bun install
```

### 3. Configure Environment Variables

```env
# Telegram Bot
TELEGRAM_BOT_TOKEN=your_bot_token_from_botfather
TELEGRAM_WEBHOOK_URL=https://your-domain.com  # For production
TELEGRAM_WEBHOOK_SECRET=your_webhook_secret

# Convex Database
CONVEX_URL=https://your-deployment.convex.cloud
CONVEX_DEPLOYMENT=your-deployment-name

# Clerk Authentication
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key

# OpenRouter AI
OPENROUTER_API_KEY=sk-or-your_openrouter_api_key

# Solana
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
BONKAI_TOKEN_ADDRESS=your_bonkai_token_mint

# General
NODE_ENV=development
PORT=3000
LOG_LEVEL=info
WEB_APP_URL=https://bonkai.vercel.app
```

### 4. Development

```bash
# Start in development mode (polling)
bun run dev

# Build for production
bun run build

# Start production (webhook mode)
bun run start
```

## 🌐 Deployment

### Production Webhook Setup

```bash
# Start webhook server
bun run webhook
```

The webhook server will:
- Set up webhook endpoint at `/webhook`
- Provide health check at `/health`
- Handle graceful shutdown
- Automatically configure Telegram webhook

### Environment Variables for Production

```env
NODE_ENV=production
TELEGRAM_WEBHOOK_URL=https://your-production-domain.com
PORT=3000
```

## 🤖 Bot Commands

| Command | Description | Auth Required |
|---------|-------------|---------------|
| `/start` | Welcome message and setup guide | ❌ |
| `/link` | Link your BonKai account | ❌ |
| `/status` | Check account status and usage | ✅ |
| `/tier` | View tier information and benefits | ❌ |
| `/wallet` | Wallet connection status | ✅ |
| `/usage` | Token usage statistics | ✅ |
| `/premium` | Premium features (Diamond tier only) | ✅ |
| `/help` | Help and support information | ❌ |
| `/unlink` | Unlink your account | ✅ |

## 🏆 User Tiers

| Tier | Requirements | Monthly Tokens | AI Models | Features |
|------|--------------|---------------|-----------|----------|
| 🆓 **FREE** | None | 10K | Basic | Standard AI responses |
| 🥉 **BRONZE** | 20 $BONKAI | 100K | Enhanced | Better AI models |
| 🥈 **SILVER** | 50 $BONKAI | 500K | Premium | Advanced features |
| 💎 **DIAMOND** | 100 $BONKAI | 2M | Best | All features + o3-mini |

## 🔐 Security Features

- **Rate Limiting**: Prevents spam and abuse
- **Token Gating**: Access based on $BONKAI holdings
- **Secure Linking**: Time-limited codes for account linking
- **Session Management**: Automatic cleanup of expired sessions
- **Error Handling**: Comprehensive error logging and user feedback

## 🔗 Account Linking Flow

1. User starts with `/link` command
2. Bot generates unique 8-character code
3. User visits web app with code
4. User logs in via Clerk authentication
5. Code verification links Telegram to BonKai account
6. Bot confirms successful linking
7. User gains access to AI features based on tier

## 📊 Usage Tracking

- **Token Consumption**: Track AI usage per user
- **Rate Limiting**: Hourly request limits by tier
- **Monthly Quotas**: Reset token limits monthly
- **Analytics**: Comprehensive usage statistics

## 🛡️ Error Handling

- **Graceful Degradation**: Bot continues working during partial failures
- **User-Friendly Messages**: Clear error messages for users
- **Comprehensive Logging**: Detailed logs for debugging
- **Automatic Recovery**: Retry mechanisms for transient failures

## 🧪 Testing

```bash
# Run type checking
bun run lint

# Format code
bun run format
```

## 📁 Project Structure

```
src/
├── config/
│   └── env.ts              # Environment configuration
├── types/
│   └── index.ts            # TypeScript types
├── lib/
│   ├── ai.ts              # AI model integration
│   ├── auth.ts            # Authentication helpers
│   ├── convex.ts          # Database client
│   └── logger.ts          # Logging utilities
├── middleware/
│   ├── auth.ts            # Authentication middleware
│   └── rateLimit.ts       # Rate limiting & token tracking
├── handlers/
│   ├── commands.ts        # Command handlers
│   ├── callbacks.ts       # Callback query handlers
│   └── messages.ts        # Message handlers
├── bot.ts                 # Bot initialization
├── index.ts               # Development entry point
└── webhook.ts             # Production webhook server
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is part of the BonKai ecosystem. See the main repository for license information.

## 🆘 Support

- **Documentation**: [bonkai.vercel.app/docs](https://bonkai.vercel.app/docs)
- **Community**: Join our Telegram community
- **Issues**: Report bugs in the main repository
- **Email**: <EMAIL>

## 🔮 Roadmap

- [ ] **Voice Messages**: Speech-to-text and text-to-speech
- [ ] **Image Analysis**: AI-powered image understanding
- [ ] **Document Processing**: Smart contract auditing
- [ ] **Portfolio Tracking**: Real-time portfolio analysis
- [ ] **Trading Signals**: AI-generated trading insights
- [ ] **Multi-language**: Support for multiple languages
- [ ] **Group Chat**: Bot functionality in group chats
- [ ] **Notifications**: Price alerts and news updates
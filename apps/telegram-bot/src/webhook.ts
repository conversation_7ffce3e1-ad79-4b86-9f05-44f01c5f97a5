#!/usr/bin/env node

import 'dotenv/config';
import { createServer } from 'node:http';
import { webhookCallback } from 'grammy';
import { bot, setBotCommands, logBotInfo } from './bot.js';
import { env } from './config/env.js';
import { logger } from './lib/logger.js';

const port = env.PORT;
const webhookUrl = env.TELEGRAM_WEBHOOK_URL;
const webhookSecret = env.TELEGRAM_WEBHOOK_SECRET;

// Create webhook handler
const handleUpdate = webhookCallback(bot, 'node', {
  timeoutMilliseconds: 30000, // 30 seconds timeout
  secretToken: webhookSecret,
});

// Create HTTP server
const server = createServer(async (req, res) => {
  // Health check endpoint
  if (req.url === '/health') {
    res.writeHead(200, { 'Content-Type': 'application/json' });
    res.end(
      JSON.stringify({
        status: 'ok',
        timestamp: new Date().toISOString(),
        bot: 'BonKai Telegram Bot',
      }),
    );
    return;
  }

  // Webhook endpoint
  if (req.url === '/webhook' && req.method === 'POST') {
    try {
      await handleUpdate(req, res);
    } catch (error) {
      logger.error('Error handling webhook:', error);
      res.writeHead(500, { 'Content-Type': 'application/json' });
      res.end(JSON.stringify({ error: 'Internal server error' }));
    }
    return;
  }

  // Default response for other requests
  res.writeHead(404, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({ error: 'Not found' }));
});

// Start webhook server
async function startWebhookServer() {
  try {
    logger.info('🚀 Starting BonKai Telegram Bot webhook server...');

    // Set bot commands
    await setBotCommands();

    // Log bot info
    await logBotInfo();

    if (!webhookUrl) {
      throw new Error('TELEGRAM_WEBHOOK_URL is required for webhook mode');
    }

    // Set webhook
    logger.info(`🔗 Setting webhook URL: ${webhookUrl}/webhook`);
    await bot.api.setWebhook(`${webhookUrl}/webhook`, {
      secret_token: webhookSecret,
      max_connections: 100,
      allowed_updates: [
        'message',
        'callback_query',
        'inline_query',
        'chosen_inline_result',
        'edited_message',
      ],
    });

    // Start server
    server.listen(port, () => {
      logger.info(`✅ Webhook server listening on port ${port}`);
      logger.info(`🌐 Webhook URL: ${webhookUrl}/webhook`);
      logger.info(`💚 Health check: ${webhookUrl}/health`);
    });

    // Graceful shutdown
    process.on('SIGINT', async () => {
      logger.info('📝 Received SIGINT, shutting down webhook server...');

      try {
        // Delete webhook
        await bot.api.deleteWebhook();
        logger.info('🗑️ Webhook deleted');
      } catch (error) {
        logger.error('Error deleting webhook:', error);
      }

      // Close server
      server.close(() => {
        logger.info('✅ Webhook server closed gracefully');
        process.exit(0);
      });
    });

    process.on('SIGTERM', async () => {
      logger.info('📝 Received SIGTERM, shutting down webhook server...');

      try {
        await bot.api.deleteWebhook();
        logger.info('🗑️ Webhook deleted');
      } catch (error) {
        logger.error('Error deleting webhook:', error);
      }

      server.close(() => {
        logger.info('✅ Webhook server closed gracefully');
        process.exit(0);
      });
    });
  } catch (error) {
    logger.error('❌ Failed to start webhook server:', error);
    process.exit(1);
  }
}

// Error handling
server.on('error', (error) => {
  logger.error('Server error:', error);
});

// Handle module being run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  startWebhookServer();
}

export { server };

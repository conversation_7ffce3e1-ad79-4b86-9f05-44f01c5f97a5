#!/usr/bin/env node

import 'dotenv/config';
import { bot, setBotCommands, logBotInfo } from './bot.js';
import { env, isDevelopment } from './config/env.js';
import { logger } from './lib/logger.js';

async function startBot() {
  try {
    logger.info('🚀 Starting BonKai Telegram Bot...');

    // Set bot commands
    await setBotCommands();

    // Log bot info
    await logBotInfo();

    if (isDevelopment) {
      // Development: Use polling
      logger.info('🔄 Starting bot in polling mode (development)');
      await bot.start({
        onStart: (info) => {
          logger.info(
            `✅ Bot @${info.username} started successfully in polling mode`,
          );
        },
      });
    } else {
      // Production: Use webhooks (handled separately)
      logger.info('🌐 Bot configured for webhook mode (production)');
      logger.info('To start webhook server, run: bun run webhook');
    }
  } catch (error) {
    logger.error('❌ Failed to start bot:', error);
    process.exit(1);
  }
}

// Handle module being run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  startBot();
}

export { bot };

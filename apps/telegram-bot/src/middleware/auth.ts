import { NextFunction } from 'grammy';
import { BotContext, AuthError, SessionData } from '../types/index.js';
import { executeConvexFunction } from '../lib/convex.js';
import { logger } from '../lib/logger.js';

// Authentication middleware
export async function authMiddleware(ctx: BotContext, next: NextFunction) {
  try {
    if (!ctx.from) {
      throw new AuthError('Unable to identify user');
    }

    const telegramId = ctx.from.id.toString();

    // Get user data from Convex
    const userData = await executeConvexFunction(
      'telegram:getUserByTelegramId',
      {
        telegramId,
      },
    );

    if (userData) {
      // User is authenticated
      ctx.session.userId = userData._id;
      ctx.session.step = 'authenticated';
      ctx.session.tier = userData.tier;
      ctx.session.lastActivity = Date.now();

      logger.debug('User authenticated', {
        userId: userData._id,
        telegramId,
        tier: userData.tier,
      });
    } else {
      // User is not authenticated
      ctx.session.step = undefined;
      ctx.session.userId = undefined;
      ctx.session.tier = undefined;

      logger.debug('User not authenticated', { telegramId });
    }

    await next();
  } catch (error) {
    logger.botError(error as Error, 'auth middleware', ctx.from?.id.toString());

    await ctx.reply(
      '❌ Authentication error. Please try again or contact support.',
      { parse_mode: 'HTML' },
    );
  }
}

// Require authentication middleware
export async function requireAuth(ctx: BotContext, next: NextFunction) {
  if (!ctx.session.userId) {
    await ctx.reply(
      '🔐 You need to link your BonKai account first.\n\n' +
        'Use /link to connect your account, or visit our web app to create one:\n' +
        'https://bonkai.vercel.app',
      {
        parse_mode: 'HTML',
        reply_markup: {
          inline_keyboard: [
            [
              { text: '🔗 Link Account', callback_data: 'link_account' },
              { text: '🌐 Web App', url: 'https://bonkai.vercel.app' },
            ],
          ],
        },
      },
    );
    return;
  }

  await next();
}

// Tier requirement middleware factory
export function requireTier(minTier: 'FREE' | 'BRONZE' | 'SILVER' | 'DIAMOND') {
  return async (ctx: BotContext, next: NextFunction) => {
    const tierValues = {
      FREE: 0,
      BRONZE: 1,
      SILVER: 2,
      DIAMOND: 3,
    };

    const userTierValue = tierValues[ctx.session.tier || 'FREE'];
    const requiredTierValue = tierValues[minTier];

    if (userTierValue < requiredTierValue) {
      const tierEmojis = {
        BRONZE: '🥉',
        SILVER: '🥈',
        DIAMOND: '💎',
      };

      await ctx.reply(
        `${tierEmojis[minTier]} This feature requires ${minTier} tier or higher.\n\n` +
          `Your current tier: ${ctx.session.tier || 'FREE'}\n` +
          `Required tier: ${minTier}\n\n` +
          'Upgrade your tier by holding more $BONKAI tokens!',
        {
          parse_mode: 'HTML',
          reply_markup: {
            inline_keyboard: [
              [
                {
                  text: '💰 Get $BONKAI',
                  url: 'https://bonkai.vercel.app/token',
                },
                { text: '📊 Check Tier', callback_data: 'check_tier' },
              ],
            ],
          },
        },
      );
      return;
    }

    await next();
  };
}

// Session cleanup middleware
export async function sessionCleanup(ctx: BotContext, next: NextFunction) {
  // Clean up old session data
  const now = Date.now();
  const lastActivity = ctx.session.lastActivity || 0;
  const sessionTimeout = 24 * 60 * 60 * 1000; // 24 hours

  if (now - lastActivity > sessionTimeout) {
    // Reset session
    ctx.session = {} as SessionData;
    logger.debug('Session expired and reset', {
      telegramId: ctx.from?.id.toString(),
    });
  }

  // Update last activity
  ctx.session.lastActivity = now;

  await next();
}

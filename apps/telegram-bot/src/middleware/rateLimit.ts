import { NextFunction } from 'grammy';
import { BotContext, RateLimitError, UserTier } from '../types/index.js';
import { executeConvexFunction } from '../lib/convex.js';
import { logger } from '../lib/logger.js';

// Rate limiting middleware
export async function rateLimitMiddleware(ctx: BotContext, next: NextFunction) {
  try {
    if (!ctx.session.userId) {
      // Allow unauthenticated users but with strict limits
      await next();
      return;
    }

    const tier = ctx.session.tier || UserTier.FREE;

    // Check rate limit
    const rateLimitInfo = await executeConvexFunction(
      'telegram:checkRateLimit',
      {
        userId: ctx.session.userId,
        tier,
      },
    );

    if (!rateLimitInfo.allowed) {
      const resetTime = new Date(rateLimitInfo.resetAt).toLocaleTimeString();

      logger.rateLimitHit(ctx.session.userId, tier, rateLimitInfo.remaining);

      await ctx.reply(
        `⚠️ Rate limit exceeded!\n\n` +
          `Your ${tier} tier allows limited requests per hour.\n` +
          `Please try again after ${resetTime}.\n\n` +
          `💡 Upgrade your tier for higher limits!`,
        {
          parse_mode: 'HTML',
          reply_markup: {
            inline_keyboard: [
              [
                {
                  text: '💰 Upgrade Tier',
                  url: 'https://bonkai.vercel.app/token',
                },
                { text: '📊 View Limits', callback_data: 'view_limits' },
              ],
            ],
          },
        },
      );
      return;
    }

    // Add rate limit info to context for potential use
    ctx.rateLimitInfo = rateLimitInfo;

    await next();
  } catch (error) {
    logger.botError(
      error as Error,
      'rate limit middleware',
      ctx.session.userId,
    );

    // Don't block the request on rate limit errors, just log
    await next();
  }
}

// Token usage tracking middleware
export async function tokenTrackingMiddleware(
  ctx: BotContext,
  next: NextFunction,
) {
  // Store original response method
  const originalReply = ctx.reply.bind(ctx);

  // Override reply to track token usage
  ctx.reply = async function (text: string, other?: any) {
    const result = await originalReply(text, other);

    // Estimate token usage (rough approximation)
    const tokenCount = Math.ceil(text.length / 4); // ~4 chars per token

    if (ctx.session.userId && tokenCount > 0) {
      try {
        await executeConvexFunction('telegram:trackTokenUsage', {
          userId: ctx.session.userId,
          tokens: tokenCount,
          model: 'telegram-response',
        });

        logger.botMessage('response_sent', ctx.session.userId, tokenCount);
      } catch (error) {
        logger.error('Failed to track token usage', error);
      }
    }

    return result;
  };

  await next();
}

// Check token limits before AI operations
export async function checkTokenLimits(
  ctx: BotContext,
  estimatedTokens: number = 100,
): Promise<boolean> {
  if (!ctx.session.userId) {
    return false;
  }

  try {
    const stats = await executeConvexFunction('telegram:getTelegramUserStats', {
      telegramId: ctx.from!.id.toString(),
    });

    if (!stats) {
      return false;
    }

    const { tokensUsedThisMonth, tokenLimit } = stats.stats;
    const remainingTokens = tokenLimit - tokensUsedThisMonth;

    if (remainingTokens < estimatedTokens) {
      logger.tokenLimitHit(
        ctx.session.userId,
        ctx.session.tier || 'FREE',
        tokensUsedThisMonth,
        tokenLimit,
      );

      await ctx.reply(
        `🚫 Monthly token limit exceeded!\n\n` +
          `Used: ${tokensUsedThisMonth.toLocaleString()} / ${tokenLimit.toLocaleString()} tokens\n` +
          `Remaining: ${remainingTokens.toLocaleString()} tokens\n\n` +
          `Your limit resets next month, or upgrade your tier for more tokens!`,
        {
          parse_mode: 'HTML',
          reply_markup: {
            inline_keyboard: [
              [
                {
                  text: '💰 Upgrade Tier',
                  url: 'https://bonkai.vercel.app/token',
                },
                { text: '📊 View Usage', callback_data: 'view_usage' },
              ],
            ],
          },
        },
      );
      return false;
    }

    return true;
  } catch (error) {
    logger.error('Failed to check token limits', error);
    return true; // Allow on error to avoid blocking
  }
}

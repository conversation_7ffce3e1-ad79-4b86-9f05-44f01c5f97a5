import { env } from '../config/env.js';

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
}

const LOG_LEVELS = {
  debug: LogLevel.DEBUG,
  info: LogLevel.INFO,
  warn: LogLevel.WARN,
  error: LogLevel.ERROR,
};

const currentLevel = LOG_LEVELS[env.LOG_LEVEL];

class Logger {
  private formatMessage(level: string, message: string, meta?: any): string {
    const timestamp = new Date().toISOString();
    const metaString = meta ? ` ${JSON.stringify(meta)}` : '';
    return `[${timestamp}] ${level.toUpperCase()}: ${message}${metaString}`;
  }

  debug(message: string, meta?: any) {
    if (currentLevel <= LogLevel.DEBUG) {
      console.debug(this.formatMessage('debug', message, meta));
    }
  }

  info(message: string, meta?: any) {
    if (currentLevel <= LogLevel.INFO) {
      console.info(this.formatMessage('info', message, meta));
    }
  }

  warn(message: string, meta?: any) {
    if (currentLevel <= LogLevel.WARN) {
      console.warn(this.formatMessage('warn', message, meta));
    }
  }

  error(message: string, error?: Error | any, meta?: any) {
    if (currentLevel <= LogLevel.ERROR) {
      const errorInfo =
        error instanceof Error
          ? { message: error.message, stack: error.stack, ...meta }
          : { error, ...meta };
      console.error(this.formatMessage('error', message, errorInfo));
    }
  }

  // Bot-specific logging methods
  botCommand(command: string, userId: string, username?: string) {
    this.info(`Bot command executed`, {
      command,
      userId,
      username,
      type: 'command',
    });
  }

  botMessage(messageType: string, userId: string, tokenCount?: number) {
    this.info(`Bot message processed`, {
      messageType,
      userId,
      tokenCount,
      type: 'message',
    });
  }

  botError(error: Error, context: string, userId?: string) {
    this.error(`Bot error in ${context}`, error, {
      userId,
      type: 'bot_error',
    });
  }

  rateLimitHit(userId: string, tier: string, remaining: number) {
    this.warn(`Rate limit hit`, {
      userId,
      tier,
      remaining,
      type: 'rate_limit',
    });
  }

  tokenLimitHit(userId: string, tier: string, used: number, limit: number) {
    this.warn(`Token limit hit`, {
      userId,
      tier,
      used,
      limit,
      type: 'token_limit',
    });
  }
}

export const logger = new Logger();

import { ConvexHttpClient } from 'convex/browser';
import { env } from '../config/env.js';

// Initialize Convex client
// Use CONVEX_DEPLOYMENT which should be the .convex.cloud URL
const convexUrl = env.CONVEX_DEPLOYMENT || env.CONVEX_URL;
export const convex = convexUrl ? new ConvexHttpClient(convexUrl) : null;

// Helper function to execute Convex functions with error handling
export async function executeConvexFunction<T>(
  functionName: string,
  args: any = {},
): Promise<T> {
  if (!convex) {
    throw new Error('Convex client not initialized. Please set CONVEX_URL or NEXT_PUBLIC_CONVEX_URL environment variable.');
  }
  try {
    return await convex.query(functionName as any, args);
  } catch (error) {
    console.error(`Error executing Convex function ${functionName}:`, error);
    throw error;
  }
}

export async function executeConvexMutation<T>(
  functionName: string,
  args: any = {},
): Promise<T> {
  if (!convex) {
    throw new Error('Convex client not initialized. Please set CONVEX_URL or NEXT_PUBLIC_CONVEX_URL environment variable.');
  }
  try {
    return await convex.mutation(functionName as any, args);
  } catch (error) {
    console.error(`Error executing Convex mutation ${functionName}:`, error);
    throw error;
  }
}

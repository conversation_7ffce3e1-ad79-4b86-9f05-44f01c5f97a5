import { Composer } from 'grammy';
import { nanoid } from 'nanoid';
import { BotContext } from '../types/index.js';
import { executeConvexFunction, executeConvexMutation } from '../lib/convex.js';
import { logger } from '../lib/logger.js';
import { env } from '../config/env.js';

export const callbacksComposer = new Composer<BotContext>();

// Link account callback
callbacksComposer.callbackQuery('link_account', async (ctx) => {
  await ctx.answerCallbackQuery();

  // Generate linking code
  const linkingCode = nanoid(8).toUpperCase();
  const expiresAt = Date.now() + 10 * 60 * 1000; // 10 minutes

  ctx.session.linkingData = {
    clerkId: '',
    code: linkingCode,
    expiresAt,
  };
  ctx.session.step = 'awaiting_link_code';

  await ctx.editMessageText(
    `🔗 **Link Your BonKai Account**\n\n` +
      `1️⃣ Copy this code: \`${linkingCode}\`\n` +
      `2️⃣ Visit the [BonKai web app](${env.WEB_APP_URL}/link)\n` +
      `3️⃣ Log in or create an account\n` +
      `4️⃣ Enter the code to link your Telegram\n\n` +
      `⏰ Code expires in 10 minutes`,
    {
      parse_mode: 'Markdown',
      disable_web_page_preview: true,
      reply_markup: {
        inline_keyboard: [
          [
            {
              text: '🌐 Open Web App',
              url: `${env.WEB_APP_URL}/link?code=${linkingCode}`,
            },
          ],
          [
            { text: '🔄 New Code', callback_data: 'new_link_code' },
            { text: '❌ Cancel', callback_data: 'cancel_link' },
          ],
        ],
      },
    },
  );
});

// Generate new linking code
callbacksComposer.callbackQuery('new_link_code', async (ctx) => {
  await ctx.answerCallbackQuery('Generating new code...');

  const linkingCode = nanoid(8).toUpperCase();
  const expiresAt = Date.now() + 10 * 60 * 1000;

  ctx.session.linkingData = {
    clerkId: '',
    code: linkingCode,
    expiresAt,
  };

  await ctx.editMessageText(
    `🔗 **New Linking Code Generated**\n\n` +
      `1️⃣ Copy this code: \`${linkingCode}\`\n` +
      `2️⃣ Visit the [BonKai web app](${env.WEB_APP_URL}/link)\n` +
      `3️⃣ Log in or create an account\n` +
      `4️⃣ Enter the code to link your Telegram\n\n` +
      `⏰ Code expires in 10 minutes`,
    {
      parse_mode: 'Markdown',
      disable_web_page_preview: true,
      reply_markup: {
        inline_keyboard: [
          [
            {
              text: '🌐 Open Web App',
              url: `${env.WEB_APP_URL}/link?code=${linkingCode}`,
            },
          ],
          [
            { text: '🔄 New Code', callback_data: 'new_link_code' },
            { text: '❌ Cancel', callback_data: 'cancel_link' },
          ],
        ],
      },
    },
  );
});

// Cancel linking
callbacksComposer.callbackQuery('cancel_link', async (ctx) => {
  await ctx.answerCallbackQuery();

  ctx.session.linkingData = undefined;
  ctx.session.step = undefined;

  await ctx.editMessageText(
    `❌ **Linking Cancelled**\n\n` +
      `You can start the linking process again anytime using /link`,
    {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [
            { text: '🔗 Link Account', callback_data: 'link_account' },
            { text: '🌐 Web App', url: env.WEB_APP_URL },
          ],
        ],
      },
    },
  );
});

// View status
callbacksComposer.callbackQuery('view_status', async (ctx) => {
  await ctx.answerCallbackQuery();

  if (!ctx.session.userId) {
    await ctx.answerCallbackQuery('⚠️ Please link your account first');
    return;
  }

  try {
    const telegramId = ctx.from!.id.toString();
    const stats = await executeConvexFunction('telegram:getTelegramUserStats', {
      telegramId,
    });

    if (!stats) {
      await ctx.answerCallbackQuery('❌ Unable to fetch status');
      return;
    }

    const { user, stats: userStats } = stats;
    const usagePercentage = Math.round(
      (userStats.tokensUsedThisMonth / userStats.tokenLimit) * 100,
    );

    const tierEmojis = {
      FREE: '🆓',
      BRONZE: '🥉',
      SILVER: '🥈',
      DIAMOND: '💎',
    };

    await ctx.editMessageText(
      `📊 **Your BonKai Status**\n\n` +
        `👤 **Account:** ${user.name || user.email}\n` +
        `🏆 **Tier:** ${tierEmojis[user.tier]} ${user.tier}\n` +
        `💰 **Token Balance:** ${user.tokenBalance.toLocaleString()}\n` +
        `${user.walletLinked ? '✅' : '❌'} **Wallet:** ${user.walletLinked ? 'Connected' : 'Not connected'}\n\n` +
        `📈 **This Month:**\n` +
        `🔥 **Used:** ${userStats.tokensUsedThisMonth.toLocaleString()} / ${userStats.tokenLimit.toLocaleString()} tokens (${usagePercentage}%)\n` +
        `⚡ **Remaining:** ${userStats.tokensRemaining.toLocaleString()} tokens\n` +
        `💬 **Chats:** ${userStats.chatCount}`,
      {
        parse_mode: 'Markdown',
        reply_markup: {
          inline_keyboard: [
            [
              { text: '🏆 Tier Benefits', callback_data: 'tier_benefits' },
              { text: '💰 Upgrade', url: `${env.WEB_APP_URL}/token` },
            ],
            [
              { text: '🔄 Refresh', callback_data: 'view_status' },
              { text: '❌ Close', callback_data: 'close_message' },
            ],
          ],
        },
      },
    );
  } catch (error) {
    logger.botError(error as Error, 'view status callback', ctx.session.userId);
    await ctx.answerCallbackQuery('❌ Error fetching status');
  }
});

// Tier benefits
callbacksComposer.callbackQuery('tier_benefits', async (ctx) => {
  await ctx.answerCallbackQuery();

  const tierInfo = {
    FREE: {
      emoji: '🆓',
      tokens: '10K',
      features: 'Basic AI models\nLimited requests\nStandard support',
    },
    BRONZE: {
      emoji: '🥉',
      tokens: '100K',
      features: 'Better AI models\nMore requests\nFaster responses',
    },
    SILVER: {
      emoji: '🥈',
      tokens: '500K',
      features: 'Premium AI models\nHigh request limits\nPriority support',
    },
    DIAMOND: {
      emoji: '💎',
      tokens: '2M',
      features:
        'Best AI models\nUnlimited requests\nPremium features\nPriority support',
    },
  };

  let message = `🏆 **Tier Benefits**\n\n`;

  Object.entries(tierInfo).forEach(([tier, info]) => {
    message += `${info.emoji} **${tier} TIER**\n`;
    message += `• ${info.tokens} tokens/month\n`;
    message += `• ${info.features.split('\n').join('\n• ')}\n\n`;
  });

  message += `💡 Hold $BONKAI tokens to upgrade automatically!`;

  await ctx.editMessageText(message, {
    parse_mode: 'Markdown',
    reply_markup: {
      inline_keyboard: [
        [
          { text: '💰 Get $BONKAI', url: `${env.WEB_APP_URL}/token` },
          { text: '🔗 Connect Wallet', url: `${env.WEB_APP_URL}/wallet` },
        ],
        [
          { text: '⬅️ Back', callback_data: 'view_status' },
          { text: '❌ Close', callback_data: 'close_message' },
        ],
      ],
    },
  });
});

// Connect wallet
callbacksComposer.callbackQuery('connect_wallet', async (ctx) => {
  await ctx.answerCallbackQuery();

  await ctx.editMessageText(
    `🔗 **Connect Your Wallet**\n\n` +
      `To connect your Solana wallet and unlock tier benefits:\n\n` +
      `1️⃣ Visit the BonKai web app\n` +
      `2️⃣ Go to Wallet section\n` +
      `3️⃣ Connect your Solana wallet\n` +
      `4️⃣ Your tier will update automatically\n\n` +
      `Supported wallets:\n` +
      `• Phantom\n` +
      `• Solflare\n` +
      `• Backpack\n` +
      `• And more...`,
    {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [{ text: '🌐 Open Wallet Page', url: `${env.WEB_APP_URL}/wallet` }],
          [
            { text: '⬅️ Back', callback_data: 'view_status' },
            { text: '❌ Close', callback_data: 'close_message' },
          ],
        ],
      },
    },
  );
});

// Confirm unlink
callbacksComposer.callbackQuery('confirm_unlink', async (ctx) => {
  await ctx.answerCallbackQuery();

  if (!ctx.session.userId) {
    await ctx.answerCallbackQuery('⚠️ No account linked');
    return;
  }

  try {
    const telegramId = ctx.from!.id.toString();

    await executeConvexMutation('telegram:unlinkTelegramUser', {
      telegramId,
    });

    // Clear session
    ctx.session = {};

    await ctx.editMessageText(
      `✅ **Account Unlinked**\n\n` +
        `Your Telegram account has been disconnected from BonKai.\n\n` +
        `You can re-link anytime using /link to restore access to AI features.`,
      {
        parse_mode: 'Markdown',
        reply_markup: {
          inline_keyboard: [
            [
              { text: '🔗 Link Again', callback_data: 'link_account' },
              { text: '🌐 Web App', url: env.WEB_APP_URL },
            ],
          ],
        },
      },
    );

    logger.info('User unlinked account', {
      telegramId,
      userId: ctx.session.userId,
    });
  } catch (error) {
    logger.botError(error as Error, 'unlink account', ctx.session.userId);
    await ctx.answerCallbackQuery('❌ Error unlinking account');
  }
});

// Cancel unlink
callbacksComposer.callbackQuery('cancel_unlink', async (ctx) => {
  await ctx.answerCallbackQuery('Cancelled');

  await ctx.editMessageText(
    `❌ **Unlink Cancelled**\n\n` +
      `Your account remains linked. You can continue using BonKai AI!`,
    {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [
            { text: '📊 View Status', callback_data: 'view_status' },
            { text: '❌ Close', callback_data: 'close_message' },
          ],
        ],
      },
    },
  );
});

// View usage
callbacksComposer.callbackQuery('view_usage', async (ctx) => {
  await ctx.answerCallbackQuery();

  if (!ctx.session.userId) {
    await ctx.answerCallbackQuery('⚠️ Please link your account first');
    return;
  }

  try {
    const telegramId = ctx.from!.id.toString();
    const stats = await executeConvexFunction('telegram:getTelegramUserStats', {
      telegramId,
    });

    if (!stats) {
      await ctx.answerCallbackQuery('❌ Unable to fetch usage data');
      return;
    }

    const { stats: userStats } = stats;
    const usagePercentage = Math.round(
      (userStats.tokensUsedThisMonth / userStats.tokenLimit) * 100,
    );

    // Create progress bar
    const barLength = 10;
    const filledLength = Math.round((usagePercentage / 100) * barLength);
    const progressBar =
      '█'.repeat(filledLength) + '░'.repeat(barLength - filledLength);

    await ctx.editMessageText(
      `📊 **Token Usage Details**\n\n` +
        `🏆 **Current Tier:** ${userStats.tier}\n\n` +
        `📅 **This Month:**\n` +
        `${progressBar} ${usagePercentage}%\n\n` +
        `🔥 **Used:** ${userStats.tokensUsedThisMonth.toLocaleString()}\n` +
        `⚡ **Remaining:** ${userStats.tokensRemaining.toLocaleString()}\n` +
        `📈 **Monthly Limit:** ${userStats.tokenLimit.toLocaleString()}\n\n` +
        `💬 **Total Chats:** ${userStats.chatCount}\n\n` +
        `🔄 Resets on 1st of each month`,
      {
        parse_mode: 'Markdown',
        reply_markup: {
          inline_keyboard: [
            [
              { text: '💰 Upgrade Tier', url: `${env.WEB_APP_URL}/token` },
              { text: '🏆 Tier Info', callback_data: 'tier_benefits' },
            ],
            [
              { text: '⬅️ Back', callback_data: 'view_status' },
              { text: '❌ Close', callback_data: 'close_message' },
            ],
          ],
        },
      },
    );
  } catch (error) {
    logger.botError(error as Error, 'view usage callback', ctx.session.userId);
    await ctx.answerCallbackQuery('❌ Error fetching usage data');
  }
});

// Close message
callbacksComposer.callbackQuery('close_message', async (ctx) => {
  await ctx.answerCallbackQuery();
  await ctx.deleteMessage();
});

// Help callback
callbacksComposer.callbackQuery('help', async (ctx) => {
  await ctx.answerCallbackQuery();

  await ctx.editMessageText(
    `🆘 **BonKai AI Help**\n\n` +
      `**Commands:**\n` +
      `/start - Welcome & setup\n` +
      `/link - Link BonKai account\n` +
      `/status - Account status\n` +
      `/tier - View tier benefits\n` +
      `/wallet - Wallet info\n` +
      `/usage - Token usage\n` +
      `/help - This help message\n\n` +
      `**Features:**\n` +
      `• AI chat about Web3/blockchain\n` +
      `• Token analysis & trading tips\n` +
      `• Smart contract help\n` +
      `• DeFi protocol guidance\n` +
      `• Market insights\n\n` +
      `**Tiers:**\n` +
      `🆓 FREE: 10K tokens/month\n` +
      `🥉 BRONZE: 100K tokens/month\n` +
      `🥈 SILVER: 500K tokens/month\n` +
      `💎 DIAMOND: 2M tokens/month`,
    {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [
            { text: '🌐 Web App', url: env.WEB_APP_URL },
            { text: '💰 Get Tokens', url: `${env.WEB_APP_URL}/token` },
          ],
          [{ text: '❌ Close', callback_data: 'close_message' }],
        ],
      },
    },
  );
});

// Commands callback
callbacksComposer.callbackQuery('commands', async (ctx) => {
  await ctx.answerCallbackQuery();

  await ctx.editMessageText(
    `⚡ **Available Commands**\n\n` +
      `🚀 \`/start\` - Welcome message\n` +
      `🔗 \`/link\` - Link your account\n` +
      `📊 \`/status\` - Account status\n` +
      `🏆 \`/tier\` - Tier information\n` +
      `👛 \`/wallet\` - Wallet status\n` +
      `📈 \`/usage\` - Token usage stats\n` +
      `💎 \`/premium\` - Premium features (Diamond only)\n` +
      `🔓 \`/unlink\` - Unlink account\n` +
      `🆘 \`/help\` - Help & support\n\n` +
      `💬 **Chat with AI:**\n` +
      `Just send any message to start chatting!\n\n` +
      `📱 **Tip:** Use the menu button for quick access to commands.`,
    {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [
            { text: '🔗 Link Account', callback_data: 'link_account' },
            { text: '📊 Status', callback_data: 'view_status' },
          ],
          [{ text: '❌ Close', callback_data: 'close_message' }],
        ],
      },
    },
  );
});

import { Composer } from 'grammy';
import { nanoid } from 'nanoid';
import { BotContext, UserTier } from '../types/index.js';
import { executeConvexFunction, executeConvexMutation } from '../lib/convex.js';
import { requireAuth, requireTier } from '../middleware/auth.js';
import { logger } from '../lib/logger.js';
import { env } from '../config/env.js';

export const commandsComposer = new Composer<BotContext>();

// Start command
commandsComposer.command('start', async (ctx) => {
  const firstName = ctx.from?.first_name || 'there';

  logger.botCommand('start', ctx.from!.id.toString(), ctx.from?.username);

  await ctx.reply(
    `🚀 Welcome to BonKai AI, ${firstName}!\n\n` +
      `I'm your Web3 and blockchain assistant, powered by advanced AI models.\n\n` +
      `🔹 Ask me about blockchain, DeFi, NFTs, trading strategies\n` +
      `🔹 Get help with smart contracts and dApp development\n` +
      `🔹 Analyze market trends and tokens\n` +
      `🔹 Learn about the Solana ecosystem\n\n` +
      `To get started, you'll need to link your BonKai account:`,
    {
      parse_mode: 'HTML',
      reply_markup: {
        inline_keyboard: [
          [
            { text: '🔗 Link Account', callback_data: 'link_account' },
            { text: '🌐 Web App', url: env.WEB_APP_URL },
          ],
          [
            { text: '💰 Get $BONKAI Tokens', url: `${env.WEB_APP_URL}/token` },
            { text: '📚 Learn More', url: `${env.WEB_APP_URL}/docs` },
          ],
          [
            { text: '🆘 Help', callback_data: 'help' },
            { text: '⚡ Commands', callback_data: 'commands' },
          ],
        ],
      },
    },
  );
});

// Help command
commandsComposer.command('help', async (ctx) => {
  logger.botCommand('help', ctx.from!.id.toString(), ctx.from?.username);

  await ctx.reply(
    `🆘 **BonKai AI Help**\n\n` +
      `**Getting Started:**\n` +
      `/start - Welcome message and setup\n` +
      `/link - Link your BonKai account\n` +
      `/status - Check your account status\n\n` +
      `**Account Management:**\n` +
      `/tier - View your tier and benefits\n` +
      `/wallet - Wallet connection status\n` +
      `/usage - Token usage statistics\n\n` +
      `**AI Features:**\n` +
      `• Simply send me any message to chat!\n` +
      `• Ask about blockchain, DeFi, trading\n` +
      `• Get help with smart contracts\n` +
      `• Analyze tokens and projects\n\n` +
      `**Tier Benefits:**\n` +
      `🆓 FREE: 10K tokens/month, basic AI\n` +
      `🥉 BRONZE: 100K tokens/month, better AI\n` +
      `🥈 SILVER: 500K tokens/month, premium AI\n` +
      `💎 DIAMOND: 2M tokens/month, best AI\n\n` +
      `Need more help? Visit our [web app](${env.WEB_APP_URL}) or check the [documentation](${env.WEB_APP_URL}/docs).`,
    {
      parse_mode: 'Markdown',
      disable_web_page_preview: true,
    },
  );
});

// Link account command
commandsComposer.command('link', async (ctx) => {
  logger.botCommand('link', ctx.from!.id.toString(), ctx.from?.username);

  // Check if already linked
  const telegramId = ctx.from!.id.toString();
  const userData = await executeConvexFunction('telegram:getUserByTelegramId', {
    telegramId,
  });

  if (userData) {
    await ctx.reply(
      `✅ Your account is already linked!\n\n` +
        `👤 **Account:** ${userData.name || userData.email}\n` +
        `🏆 **Tier:** ${userData.tier}\n` +
        `💰 **Token Balance:** ${userData.tokenBalance.toLocaleString()}\n\n` +
        `Use /status for more details or /unlink to disconnect.`,
      {
        parse_mode: 'Markdown',
        reply_markup: {
          inline_keyboard: [
            [
              { text: '📊 View Status', callback_data: 'view_status' },
              { text: '🔓 Unlink', callback_data: 'confirm_unlink' },
            ],
          ],
        },
      },
    );
    return;
  }

  // Generate linking code
  const linkingCode = nanoid(8).toUpperCase();
  const expiresAt = Date.now() + 10 * 60 * 1000; // 10 minutes

  // Store linking code in session
  ctx.session.linkingData = {
    clerkId: '', // Will be filled when user visits web app
    code: linkingCode,
    expiresAt,
  };
  ctx.session.step = 'awaiting_link_code';

  await ctx.reply(
    `🔗 **Link Your BonKai Account**\n\n` +
      `1️⃣ Copy this code: \`${linkingCode}\`\n` +
      `2️⃣ Visit the [BonKai web app](${env.WEB_APP_URL}/link)\n` +
      `3️⃣ Log in or create an account\n` +
      `4️⃣ Enter the code to link your Telegram\n\n` +
      `⏰ Code expires in 10 minutes\n\n` +
      `Don't have an account? Create one at [bonkai.vercel.app](${env.WEB_APP_URL})`,
    {
      parse_mode: 'Markdown',
      disable_web_page_preview: true,
      reply_markup: {
        inline_keyboard: [
          [
            {
              text: '🌐 Open Web App',
              url: `${env.WEB_APP_URL}/link?code=${linkingCode}`,
            },
          ],
          [
            { text: '🔄 Generate New Code', callback_data: 'new_link_code' },
            { text: '❌ Cancel', callback_data: 'cancel_link' },
          ],
        ],
      },
    },
  );
});

// Status command (requires auth)
commandsComposer.command('status', requireAuth, async (ctx) => {
  logger.botCommand('status', ctx.from!.id.toString(), ctx.from?.username);

  try {
    const telegramId = ctx.from!.id.toString();
    const stats = await executeConvexFunction('telegram:getTelegramUserStats', {
      telegramId,
    });

    if (!stats) {
      await ctx.reply('❌ Unable to fetch your status. Please try again.');
      return;
    }

    const { user, stats: userStats } = stats;
    const usagePercentage = Math.round(
      (userStats.tokensUsedThisMonth / userStats.tokenLimit) * 100,
    );

    const tierEmojis = {
      FREE: '🆓',
      BRONZE: '🥉',
      SILVER: '🥈',
      DIAMOND: '💎',
    };

    await ctx.reply(
      `📊 **Your BonKai Status**\n\n` +
        `👤 **Account:** ${user.name || user.email}\n` +
        `🏆 **Tier:** ${tierEmojis[user.tier]} ${user.tier}\n` +
        `💰 **Token Balance:** ${user.tokenBalance.toLocaleString()}\n` +
        `${user.walletLinked ? '✅' : '❌'} **Wallet:** ${user.walletLinked ? 'Connected' : 'Not connected'}\n\n` +
        `📈 **This Month:**\n` +
        `🔥 **Used:** ${userStats.tokensUsedThisMonth.toLocaleString()} / ${userStats.tokenLimit.toLocaleString()} tokens (${usagePercentage}%)\n` +
        `⚡ **Remaining:** ${userStats.tokensRemaining.toLocaleString()} tokens\n` +
        `💬 **Chats:** ${userStats.chatCount}\n\n` +
        `🔄 Usage resets monthly`,
      {
        parse_mode: 'Markdown',
        reply_markup: {
          inline_keyboard: [
            [
              { text: '🏆 View Tier Benefits', callback_data: 'tier_benefits' },
              { text: '💰 Upgrade Tier', url: `${env.WEB_APP_URL}/token` },
            ],
            user.walletLinked
              ? []
              : [
                  {
                    text: '🔗 Connect Wallet',
                    callback_data: 'connect_wallet',
                  },
                ],
          ].filter((row) => row.length > 0),
        },
      },
    );
  } catch (error) {
    logger.botError(error as Error, 'status command', ctx.session.userId);
    await ctx.reply('❌ Unable to fetch your status. Please try again.');
  }
});

// Tier command
commandsComposer.command('tier', async (ctx) => {
  logger.botCommand('tier', ctx.from!.id.toString(), ctx.from?.username);

  const currentTier = ctx.session.tier || 'FREE';

  const tierInfo = {
    FREE: {
      emoji: '🆓',
      tokens: '10K',
      features: 'Basic AI, limited requests',
    },
    BRONZE: {
      emoji: '🥉',
      tokens: '100K',
      features: 'Better AI models, more requests',
    },
    SILVER: {
      emoji: '🥈',
      tokens: '500K',
      features: 'Premium AI, priority support',
    },
    DIAMOND: {
      emoji: '💎',
      tokens: '2M',
      features: 'Best AI models, unlimited features',
    },
  };

  const requirements = {
    BRONZE: '20 $BONKAI',
    SILVER: '50 $BONKAI',
    DIAMOND: '100 $BONKAI',
  };

  let message = `🏆 **Tier Information**\n\n`;
  message += `Your current tier: ${tierInfo[currentTier].emoji} **${currentTier}**\n\n`;

  Object.entries(tierInfo).forEach(([tier, info]) => {
    const current = tier === currentTier ? ' ⬅️ *Your tier*' : '';
    const requirement =
      tier !== 'FREE'
        ? ` (${requirements[tier as keyof typeof requirements]})`
        : '';

    message += `${info.emoji} **${tier}**${requirement}${current}\n`;
    message += `   • ${info.tokens} tokens/month\n`;
    message += `   • ${info.features}\n\n`;
  });

  message += `💡 Hold more $BONKAI tokens to upgrade your tier automatically!`;

  await ctx.reply(message, {
    parse_mode: 'Markdown',
    reply_markup: {
      inline_keyboard: [
        [
          { text: '💰 Get $BONKAI', url: `${env.WEB_APP_URL}/token` },
          { text: '🔗 Connect Wallet', callback_data: 'connect_wallet' },
        ],
        [{ text: '📊 Check Status', callback_data: 'view_status' }],
      ],
    },
  });
});

// Wallet command
commandsComposer.command('wallet', requireAuth, async (ctx) => {
  logger.botCommand('wallet', ctx.from!.id.toString(), ctx.from?.username);

  try {
    const telegramId = ctx.from!.id.toString();
    const stats = await executeConvexFunction('telegram:getTelegramUserStats', {
      telegramId,
    });

    if (!stats) {
      await ctx.reply('❌ Unable to fetch wallet information.');
      return;
    }

    const { user } = stats;

    if (user.walletLinked) {
      const shortAddress = `${user.walletAddress!.slice(0, 6)}...${user.walletAddress!.slice(-4)}`;
      await ctx.reply(
        `✅ **Wallet Connected**\n\n` +
          `🔗 **Address:** \`${shortAddress}\`\n` +
          `🏆 **Tier:** ${user.tier}\n` +
          `💰 **Balance:** ${user.tokenBalance.toLocaleString()} $BONKAI\n\n` +
          `Your tier is automatically updated based on your token balance.`,
        {
          parse_mode: 'Markdown',
          reply_markup: {
            inline_keyboard: [
              [
                {
                  text: '🌐 View on Explorer',
                  url: `https://explorer.solana.com/address/${user.walletAddress}`,
                },
              ],
              [
                {
                  text: '🔓 Disconnect Wallet',
                  callback_data: 'disconnect_wallet',
                },
              ],
            ],
          },
        },
      );
    } else {
      await ctx.reply(
        `❌ **No Wallet Connected**\n\n` +
          `Connect your Solana wallet to:\n` +
          `• Automatically update your tier\n` +
          `• Access premium features\n` +
          `• Earn rewards and airdrops\n\n` +
          `Visit the web app to connect your wallet securely.`,
        {
          parse_mode: 'Markdown',
          reply_markup: {
            inline_keyboard: [
              [{ text: '🔗 Connect Wallet', url: `${env.WEB_APP_URL}/wallet` }],
            ],
          },
        },
      );
    }
  } catch (error) {
    logger.botError(error as Error, 'wallet command', ctx.session.userId);
    await ctx.reply('❌ Unable to fetch wallet information. Please try again.');
  }
});

// Usage command
commandsComposer.command('usage', requireAuth, async (ctx) => {
  logger.botCommand('usage', ctx.from!.id.toString(), ctx.from?.username);

  try {
    const telegramId = ctx.from!.id.toString();
    const stats = await executeConvexFunction('telegram:getTelegramUserStats', {
      telegramId,
    });

    if (!stats) {
      await ctx.reply('❌ Unable to fetch usage statistics.');
      return;
    }

    const { stats: userStats } = stats;
    const usagePercentage = Math.round(
      (userStats.tokensUsedThisMonth / userStats.tokenLimit) * 100,
    );

    // Create progress bar
    const barLength = 10;
    const filledLength = Math.round((usagePercentage / 100) * barLength);
    const progressBar =
      '█'.repeat(filledLength) + '░'.repeat(barLength - filledLength);

    await ctx.reply(
      `📊 **Token Usage Statistics**\n\n` +
        `🏆 **Tier:** ${userStats.tier}\n` +
        `📅 **This Month:**\n\n` +
        `${progressBar} ${usagePercentage}%\n\n` +
        `🔥 **Used:** ${userStats.tokensUsedThisMonth.toLocaleString()} tokens\n` +
        `⚡ **Remaining:** ${userStats.tokensRemaining.toLocaleString()} tokens\n` +
        `📈 **Limit:** ${userStats.tokenLimit.toLocaleString()} tokens\n\n` +
        `💬 **Total Chats:** ${userStats.chatCount}\n\n` +
        `🔄 Usage resets on the 1st of each month`,
      {
        parse_mode: 'Markdown',
        reply_markup: {
          inline_keyboard: [
            [
              { text: '💰 Upgrade for More', url: `${env.WEB_APP_URL}/token` },
              { text: '📊 View Status', callback_data: 'view_status' },
            ],
          ],
        },
      },
    );
  } catch (error) {
    logger.botError(error as Error, 'usage command', ctx.session.userId);
    await ctx.reply('❌ Unable to fetch usage statistics. Please try again.');
  }
});

// Premium command (Diamond tier only)
commandsComposer.command(
  'premium',
  requireAuth,
  requireTier('DIAMOND'),
  async (ctx) => {
    logger.botCommand('premium', ctx.from!.id.toString(), ctx.from?.username);

    await ctx.reply(
      `💎 **Premium Features**\n\n` +
        `Welcome to BonKai Premium! As a Diamond tier member, you have access to:\n\n` +
        `🤖 **Advanced AI Models**\n` +
        `• GPT-4o for complex queries\n` +
        `• o3-mini for reasoning tasks\n` +
        `• DALL-E 3 for image generation\n\n` +
        `⚡ **Enhanced Limits**\n` +
        `• 2M tokens per month\n` +
        `• 1000 requests per hour\n` +
        `• Priority processing\n\n` +
        `🔧 **Exclusive Features**\n` +
        `• Advanced market analysis\n` +
        `• Portfolio optimization\n` +
        `• Custom trading strategies\n` +
        `• Priority support\n\n` +
        `Thank you for being a premium member! 🙏`,
      {
        parse_mode: 'Markdown',
        reply_markup: {
          inline_keyboard: [
            [
              {
                text: '🌐 Premium Dashboard',
                url: `${env.WEB_APP_URL}/premium`,
              },
              {
                text: '📞 Priority Support',
                url: `${env.WEB_APP_URL}/support`,
              },
            ],
          ],
        },
      },
    );
  },
);

// Unlink command
commandsComposer.command('unlink', requireAuth, async (ctx) => {
  logger.botCommand('unlink', ctx.from!.id.toString(), ctx.from?.username);

  await ctx.reply(
    `⚠️ **Unlink Account**\n\n` +
      `Are you sure you want to unlink your BonKai account?\n\n` +
      `This will:\n` +
      `• Disconnect your Telegram from BonKai\n` +
      `• Stop AI chat functionality\n` +
      `• Remove access to premium features\n\n` +
      `You can re-link anytime using /link`,
    {
      parse_mode: 'Markdown',
      reply_markup: {
        inline_keyboard: [
          [
            { text: '✅ Yes, Unlink', callback_data: 'confirm_unlink' },
            { text: '❌ Cancel', callback_data: 'cancel_unlink' },
          ],
        ],
      },
    },
  );
});

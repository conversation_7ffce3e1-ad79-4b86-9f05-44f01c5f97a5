{"name": "@bonkai/telegram-bot", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "bun run --watch src/index.ts", "build": "bun build src/index.ts --outdir dist --target node", "start": "bun run dist/index.js", "lint": "biome lint --write --unsafe", "format": "biome format --write", "webhook": "bun run src/webhook.ts"}, "dependencies": {"grammy": "^1.21.1", "@grammyjs/menu": "^1.2.2", "@grammyjs/conversations": "^1.2.0", "@grammyjs/stateless-question": "^3.0.0", "@grammyjs/ratelimiter": "^1.2.1", "@clerk/backend": "^1.15.6", "@solana/web3.js": "^1.98.2", "convex": "^1.25.2", "ai": "5.0.0-beta.6", "@ai-sdk/provider": "2.0.0-beta.1", "@openrouter/ai-sdk-provider": "^0.0.5", "openai": "^5.8.2", "dotenv": "^16.4.5", "zod": "^3.25.68", "nanoid": "^5.0.8", "ws": "^8.18.0"}, "devDependencies": {"@types/ws": "^8.5.10", "@types/node": "^22.8.6", "typescript": "^5.6.3"}}
# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_WEBHOOK_URL=https://your-domain.com
TELEGRAM_WEBHOOK_SECRET=your_webhook_secret_here

# Convex Configuration
CONVEX_URL=https://your-convex-deployment.convex.cloud
CONVEX_DEPLOYMENT=your-deployment-name

# Clerk Configuration
CLERK_SECRET_KEY=sk_test_your_clerk_secret_key
CLERK_WEBHOOK_SECRET=whsec_your_webhook_secret

# OpenRouter Configuration
OPENROUTER_API_KEY=sk-or-your-openrouter-api-key

# Solana Configuration
SOLANA_RPC_URL=https://api.devnet.solana.com
BONKAI_TOKEN_ADDRESS=your_token_mint_address

# General Configuration
NODE_ENV=development
PORT=3000
LOG_LEVEL=info

# Rate Limiting (Optional - Redis)
REDIS_URL=redis://localhost:6379

# App URLs
WEB_APP_URL=https://bonkai.vercel.app
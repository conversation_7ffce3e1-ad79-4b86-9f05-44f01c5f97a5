#!/bin/bash

# BonKai Telegram Bot Deployment Script

set -e  # Exit on any error

echo "🚀 Starting BonKai Telegram Bot deployment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_error ".env file not found!"
    print_status "Please copy .env.example to .env and configure your environment variables"
    exit 1
fi

# Check required environment variables
source .env

required_vars=("TELEGRAM_BOT_TOKEN" "CONVEX_URL" "CLERK_SECRET_KEY" "OPENROUTER_API_KEY")
missing_vars=()

for var in "${required_vars[@]}"; do
    if [ -z "${!var}" ]; then
        missing_vars+=("$var")
    fi
done

if [ ${#missing_vars[@]} -ne 0 ]; then
    print_error "Missing required environment variables:"
    printf '%s\n' "${missing_vars[@]}"
    exit 1
fi

print_status "Environment variables validated ✓"

# Install dependencies
print_status "Installing dependencies..."
bun install

# Type checking
print_status "Running type checking..."
bunx tsc --noEmit

# Linting
print_status "Running linter..."
bun run lint

# Build the project
print_status "Building project..."
bun run build

print_status "Build completed successfully ✓"

# Check deployment mode
if [ "$NODE_ENV" = "production" ]; then
    print_status "🌐 Production deployment detected"
    
    if [ -z "$TELEGRAM_WEBHOOK_URL" ]; then
        print_error "TELEGRAM_WEBHOOK_URL is required for production deployment"
        exit 1
    fi
    
    print_status "Webhook URL: $TELEGRAM_WEBHOOK_URL"
    print_status "Ready to start webhook server with: bun run webhook"
    
else
    print_status "🔄 Development deployment detected"
    print_status "Ready to start polling with: bun run dev"
fi

# Success message
echo ""
print_status "🎉 Deployment completed successfully!"
echo ""
echo "Next steps:"
if [ "$NODE_ENV" = "production" ]; then
    echo "  • Start the webhook server: bun run webhook"
    echo "  • Check health endpoint: curl $TELEGRAM_WEBHOOK_URL/health"
else
    echo "  • Start development server: bun run dev"
fi
echo "  • Test bot: Send /start to your bot on Telegram"
echo "  • Monitor logs: Check console output for any issues"
echo ""
print_status "Bot deployment ready! 🤖"
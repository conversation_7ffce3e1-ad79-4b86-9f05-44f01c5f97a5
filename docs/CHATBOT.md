# Next.js AI Chatbot Documentation

## Overview

The Next.js AI Chatbot is a sophisticated, full-featured conversational AI application built with modern web technologies. It provides a complete chat interface with advanced features like artifact generation, multimodal input, authentication, and real-time streaming responses.

## Architecture

### Tech Stack
- **Framework**: Next.js 15 with App Router
- **AI Integration**: Vercel AI SDK v5 (beta)
- **Database**: Convex
- **Authentication**: NextAuth.js v5 (beta)
- **UI Components**: shadcn/ui with Radix UI primitives
- **Styling**: Tailwind CSS
- **State Management**: SWR for data fetching
- **File Storage**: Vercel Blob
- **Testing**: Playwright

### Core Features

#### 1. **AI Models & Providers**
- **Primary Model**: xAI Grok-2-Vision-1212 for general chat
- **Reasoning Model**: Grok-3-Mini-Beta with reasoning middleware
- **Image Model**: Grok-2-Image for image generation
- **Flexible Provider System**: Easily switchable between different AI providers

#### 2. **Artifact System**
The chatbot supports creating and editing various types of artifacts:
- **Code**: Interactive code editor with syntax highlighting (CodeMirror)
- **Text**: Rich text documents with ProseMirror editor
- **Sheets**: Spreadsheet functionality with data grid
- **Images**: AI-generated images with editing capabilities

#### 3. **Authentication System**
- **Regular Users**: Email/password authentication
- **Guest Users**: Temporary accounts for anonymous usage
- **Session Management**: JWT-based sessions with NextAuth.js

#### 4. **Database Schema**
```sql
- Users: id, email, password
- Chats: id, title, userId, visibility, createdAt
- Messages: id, chatId, role, content, createdAt
- Documents: id, title, kind, content, userId
- Votes: chatId, messageId, isUpvoted, userId
```

#### 5. **Real-time Features**
- **Streaming Responses**: Real-time message streaming
- **Data Streams**: Live artifact updates
- **Resumable Streams**: Interrupted conversations can be resumed
- **Auto-resume**: Automatic conversation continuation

## Key Components

### Chat Interface (`components/chat.tsx`)
- Main chat container managing conversation state
- Integrates with AI SDK's `useChat` hook
- Handles message history, attachments, and streaming
- Manages artifact display and interactions

### Multimodal Input (`components/multimodal-input.tsx`)
- Rich input interface supporting text and file attachments
- Drag-and-drop file upload
- Suggested actions and auto-complete
- Mobile-responsive design

### Artifact System (`components/artifact.tsx`)
- Dynamic artifact rendering based on type
- Real-time collaborative editing
- Version control and history
- Export/import functionality

### Sidebar (`components/app-sidebar.tsx`)
- Chat history navigation
- User profile management
- Settings and preferences
- Collapsible responsive design

## AI Tools Integration

### Built-in Tools
1. **Document Creation** (`lib/ai/tools/create-document.ts`)
   - Creates various document types
   - Streams content generation
   - Saves to database

2. **Document Updates** (`lib/ai/tools/update-document.ts`)
   - Modifies existing documents
   - Handles incremental changes
   - Maintains version history

3. **Weather Information** (`lib/ai/tools/get-weather.ts`)
   - Provides weather data
   - Location-based queries
   - Real-time updates

4. **Suggestion Requests** (`lib/ai/tools/request-suggestions.ts`)
   - Generates contextual suggestions
   - Improves user experience
   - Adaptive recommendations

## API Endpoints

### Chat API (`/api/chat`)
- **POST**: Create new chat or send message
- **GET**: Retrieve chat history
- **DELETE**: Remove chat

### Document API (`/api/document`)
- **POST**: Create/update documents
- **GET**: Retrieve document content

### File Upload API (`/api/files/upload`)
- **POST**: Upload attachments
- Supports multiple file types
- Integrates with Vercel Blob storage

### History API (`/api/history`)
- **GET**: Paginated chat history
- User-specific filtering
- Search functionality

## Configuration

### Environment Variables
```env
# AI Provider
XAI_API_KEY=your_xai_api_key

# Database
POSTGRES_URL=your_postgres_connection_string

# Authentication
AUTH_SECRET=your_auth_secret
NEXTAUTH_URL=your_app_url

# Storage
BLOB_READ_WRITE_TOKEN=your_vercel_blob_token

# Optional: Redis for caching
REDIS_URL=your_redis_url
```

### Model Configuration (`lib/ai/models.ts`)
- Configurable model selection
- Support for multiple model types
- Easy provider switching

## Security Features

### Authentication
- Secure password hashing with bcrypt
- JWT token management
- Session validation middleware

### Data Protection
- User-specific data isolation
- Private/public chat visibility
- Secure file upload validation

### Rate Limiting
- Message count tracking
- User-based limitations
- Abuse prevention

## Performance Optimizations

### Streaming
- Real-time response streaming
- Chunked data transfer
- Reduced perceived latency

### Caching
- SWR for client-side caching
- Database query optimization
- Static asset optimization

### Code Splitting
- Dynamic imports for artifacts
- Lazy loading components
- Optimized bundle sizes

## Development Workflow

### Scripts
```bash
# Development
pnpm dev              # Start development server
pnpm build           # Build for production
pnpm start           # Start production server

# Convex Database
bun convex:dev       # Start Convex development server
bun convex:deploy    # Deploy Convex functions
bun convex:dashboard # Open Convex dashboard

# Testing
pnpm test            # Run Playwright tests
pnpm lint            # Lint code
pnpm format          # Format code
```

### Testing Strategy
- End-to-end testing with Playwright
- Component testing for UI elements
- API route testing
- Mock AI responses for consistent testing

## Deployment

### Vercel Deployment
1. Connect GitHub repository
2. Configure environment variables
3. Set up database (Neon/Vercel Postgres)
4. Configure blob storage
5. Deploy with automatic CI/CD

### Self-hosting Options
- Docker containerization support
- Database migration scripts
- Environment configuration templates

## Customization Guide

### Adding New AI Tools
1. Create tool definition in `lib/ai/tools/`
2. Register in chat API route
3. Add UI components if needed
4. Update documentation

### Custom Artifact Types
1. Define artifact kind in `components/artifact.tsx`
2. Create client/server handlers in `artifacts/`
3. Add database schema if needed
4. Implement UI components

### Styling Customization
- Modify `tailwind.config.ts` for theme changes
- Update CSS variables in `globals.css`
- Customize component styles in `components/ui/`

## Troubleshooting

### Common Issues
1. **Database Connection**: Check POSTGRES_URL format
2. **AI API Errors**: Verify API keys and rate limits
3. **File Upload Issues**: Check blob storage configuration
4. **Authentication Problems**: Validate AUTH_SECRET and session config

### Debug Mode
- Enable detailed logging in development
- Use browser dev tools for client-side debugging
- Check server logs for API issues

## Contributing

### Code Standards
- TypeScript strict mode
- ESLint + Biome for linting
- Prettier for formatting
- Conventional commits

### Pull Request Process
1. Fork repository
2. Create feature branch
3. Add tests for new features
4. Update documentation
5. Submit pull request

## Advanced Features

### Reasoning Mode
- **Grok-3-Mini-Beta**: Advanced reasoning capabilities
- **Think Tags**: Structured reasoning output
- **Step-by-step Analysis**: Detailed problem-solving process

### Multimodal Capabilities
- **File Attachments**: Support for various file types
- **Image Processing**: AI-powered image analysis
- **Document Parsing**: Extract text from PDFs and documents

### Collaborative Features
- **Shared Chats**: Public/private visibility settings
- **Real-time Updates**: Live collaboration on artifacts
- **Version History**: Track document changes over time

### Mobile Optimization
- **Responsive Design**: Optimized for all screen sizes
- **Touch Interactions**: Mobile-friendly gestures
- **Progressive Web App**: Installable on mobile devices

## Code Examples

### Creating a Custom AI Tool
```typescript
// lib/ai/tools/custom-tool.ts
import { tool } from 'ai';
import { z } from 'zod';

export const customTool = tool({
  description: 'Custom tool description',
  inputSchema: z.object({
    input: z.string(),
  }),
  execute: async ({ input }) => {
    // Tool implementation
    return { result: 'processed' };
  },
});
```

### Adding a New Artifact Type
```typescript
// artifacts/custom/server.ts
import { createDocumentHandler } from '@/lib/artifacts/server';

export const customDocumentHandler = createDocumentHandler({
  kind: 'custom',
  onCreateDocument: async ({ title, dataStream }) => {
    // Generate content
    return generatedContent;
  },
  onUpdateDocument: async ({ document, description }) => {
    // Update logic
    return updatedContent;
  },
});
```

### Custom React Component
```tsx
// components/custom-component.tsx
'use client';

import { useChat } from '@ai-sdk/react';

export function CustomComponent() {
  const { messages, input, handleInputChange, handleSubmit } = useChat();

  return (
    <div>
      {/* Component implementation */}
    </div>
  );
}
```

## Performance Metrics

### Benchmarks
- **First Contentful Paint**: < 1.5s
- **Time to Interactive**: < 3s
- **Message Response Time**: < 500ms (streaming start)
- **File Upload Speed**: Optimized with chunked uploads

### Optimization Techniques
- **Server Components**: Reduced client-side JavaScript
- **Streaming SSR**: Faster page loads
- **Image Optimization**: Next.js Image component
- **Bundle Analysis**: Regular bundle size monitoring

## Monitoring & Analytics

### Built-in Analytics
- **Vercel Analytics**: Page views and performance
- **OpenTelemetry**: Distributed tracing
- **Error Tracking**: Comprehensive error logging

### Custom Metrics
- **Chat Engagement**: Message frequency and length
- **Feature Usage**: Artifact creation statistics
- **User Retention**: Session duration and return rates

## Accessibility

### WCAG Compliance
- **Keyboard Navigation**: Full keyboard accessibility
- **Screen Reader Support**: ARIA labels and descriptions
- **Color Contrast**: WCAG AA compliant colors
- **Focus Management**: Proper focus indicators

### Inclusive Design
- **Dark/Light Mode**: System preference detection
- **Font Scaling**: Responsive typography
- **Reduced Motion**: Respects user preferences

## Internationalization

### Multi-language Support
- **i18n Ready**: Prepared for localization
- **RTL Support**: Right-to-left language compatibility
- **Date/Time Formatting**: Locale-aware formatting

## Security Best Practices

### Data Protection
- **Encryption**: Data encrypted at rest and in transit
- **Input Sanitization**: XSS prevention
- **SQL Injection Protection**: Parameterized queries
- **CSRF Protection**: Built-in CSRF tokens

### Privacy Features
- **Data Retention**: Configurable data retention policies
- **User Data Export**: GDPR compliance features
- **Anonymous Usage**: Guest user support

## License

This project is open source and available under the MIT License.

## Resources

### Documentation Links
- [Next.js Documentation](https://nextjs.org/docs)
- [Vercel AI SDK](https://sdk.vercel.ai/docs)
- [shadcn/ui Components](https://ui.shadcn.com)
- [Drizzle ORM](https://orm.drizzle.team)

### Community
- [GitHub Repository](https://github.com/vercel/ai-chatbot)
- [Discord Community](https://discord.gg/vercel)
- [Twitter Updates](https://twitter.com/vercel)

### Support
- [GitHub Issues](https://github.com/vercel/ai-chatbot/issues)
- [Documentation Site](https://chat-sdk.dev)
- [Vercel Support](https://vercel.com/support)

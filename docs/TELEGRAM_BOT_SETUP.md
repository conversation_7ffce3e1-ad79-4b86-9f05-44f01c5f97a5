# BonKai Telegram Bot Setup Guide

This guide will help you set up and deploy the BonKai Telegram Bot, which integrates with the BonKai Web3 AI ecosystem.

## 📋 Prerequisites

Before starting, ensure you have:

- [Bun](https://bun.sh/) installed (JavaScript runtime)
- A Telegram account
- Access to the BonKai Convex database
- Clerk authentication setup
- OpenRouter API access
- (Optional) Solana RPC endpoint

## 🤖 Step 1: Create Telegram Bot

1. **Start BotFather conversation**
   - Open Telegram and search for `@BotFather`
   - Send `/start` to begin

2. **Create new bot**
   ```
   /newbot
   ```
   - Choose a name for your bot (e.g., "BonKai AI Assistant")
   - Choose a username ending in "bot" (e.g., "bonkai_ai_bot")

3. **Save the bot token**
   - BotFather will provide a token like: `*********:ABCdefGhIJKlmNoPQRsTUVwxyZ`
   - Save this token securely - you'll need it for configuration

4. **Configure bot settings** (optional)
   ```
   /setdescription
   ```
   - Set description: "AI-powered Web3 and blockchain assistant"
   
   ```
   /setabouttext
   ```
   - Set about: "BonKai AI helps with blockchain, DeFi, NFTs, and Web3 development"

   ```
   /setuserpic
   ```
   - Upload the BonKai logo as bot avatar

## ⚙️ Step 2: Environment Configuration

1. **Navigate to the telegram bot directory**
   ```bash
   cd apps/telegram-bot
   ```

2. **Copy environment template**
   ```bash
   cp .env.example .env
   ```

3. **Configure environment variables**
   Edit `.env` file with your specific values:

   ```env
   # Telegram Bot Configuration
   TELEGRAM_BOT_TOKEN=*********:ABCdefGhIJKlmNoPQRsTUVwxyZ
   TELEGRAM_WEBHOOK_URL=https://your-domain.com  # For production only
   TELEGRAM_WEBHOOK_SECRET=your_random_secret_key

   # Convex Database
   CONVEX_URL=https://your-deployment.convex.cloud
   CONVEX_DEPLOYMENT=your-deployment-name

   # Clerk Authentication
   CLERK_SECRET_KEY=sk_test_your_clerk_secret_key
   CLERK_WEBHOOK_SECRET=whsec_your_webhook_secret

   # OpenRouter AI
   OPENROUTER_API_KEY=sk-or-your_openrouter_api_key

   # Solana Blockchain
   SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
   BONKAI_TOKEN_ADDRESS=your_bonkai_token_mint_address

   # General Configuration
   NODE_ENV=development
   PORT=3000
   LOG_LEVEL=info
   WEB_APP_URL=https://bonkai.vercel.app
   ```

## 🛠️ Step 3: Installation

1. **Install dependencies**
   ```bash
   bun install
   ```

2. **Type checking**
   ```bash
   bunx tsc --noEmit
   ```

3. **Lint and format**
   ```bash
   bun run lint
   bun run format
   ```

## 🚀 Step 4: Development Setup

1. **Start development server** (uses polling)
   ```bash
   bun run dev
   ```

2. **Test the bot**
   - Open Telegram
   - Search for your bot username
   - Send `/start` command
   - You should receive a welcome message

3. **Test linking flow**
   - Send `/link` command
   - Visit the provided web app URL
   - Complete the account linking process

## 🌐 Step 5: Production Deployment

### Option A: Webhook Mode (Recommended)

1. **Set production environment**
   ```env
   NODE_ENV=production
   TELEGRAM_WEBHOOK_URL=https://your-production-domain.com
   ```

2. **Build for production**
   ```bash
   bun run build
   ```

3. **Start webhook server**
   ```bash
   bun run webhook
   ```

4. **Verify deployment**
   - Check health endpoint: `curl https://your-domain.com/health`
   - Send test message to bot

### Option B: Polling Mode (Development/Testing)

1. **Start with polling**
   ```bash
   NODE_ENV=development bun run start
   ```

## 🔧 Step 6: Convex Database Setup

The bot requires these Convex functions to be deployed:

1. **Ensure Convex is running**
   ```bash
   # From project root
   bun run convex:dev
   ```

2. **Verify required functions exist**
   - `telegram:linkTelegramUser`
   - `telegram:getUserByTelegramId`
   - `telegram:unlinkTelegramUser`
   - `telegram:trackTokenUsage`
   - `telegram:checkRateLimit`
   - `telegram:getTelegramUserStats`

3. **Test database connection**
   ```bash
   # Check Convex dashboard
   bun run convex:dashboard
   ```

## 🔐 Step 7: Clerk Integration

1. **Configure Clerk webhook** (optional)
   - Set up webhook endpoint for user updates
   - Add webhook secret to environment

2. **Test authentication flow**
   - Generate linking code via bot
   - Complete linking in web app
   - Verify user appears in Convex database

## 📊 Step 8: Monitoring & Logging

1. **Check bot logs**
   ```bash
   # Development
   bun run dev

   # Production
   bun run webhook
   ```

2. **Monitor key metrics**
   - User registrations
   - Token usage
   - Error rates
   - Response times

3. **Set up alerts** (optional)
   - Configure log aggregation
   - Set up error notifications
   - Monitor resource usage

## 🧪 Step 9: Testing

1. **Test all commands**
   ```
   /start - Welcome message
   /link - Account linking
   /status - User status
   /tier - Tier information
   /wallet - Wallet status
   /usage - Token usage
   /help - Help message
   ```

2. **Test AI conversations**
   - Send text messages
   - Verify AI responses
   - Check token tracking

3. **Test rate limiting**
   - Send multiple rapid requests
   - Verify limits are enforced
   - Check error messages

## 🔄 Step 10: Maintenance

### Regular Tasks

1. **Monitor logs daily**
   ```bash
   tail -f logs/bot.log
   ```

2. **Check database health**
   - Monitor Convex dashboard
   - Review query performance
   - Check storage usage

3. **Update dependencies monthly**
   ```bash
   bun update
   ```

### Backup & Recovery

1. **Database backups**
   - Convex handles automatic backups
   - Export critical data regularly

2. **Configuration backups**
   - Store environment variables securely
   - Backup bot settings

## 🚨 Troubleshooting

### Common Issues

1. **Bot not responding**
   - Check bot token is correct
   - Verify network connectivity
   - Check Convex database status

2. **Authentication errors**
   - Verify Clerk secret key
   - Check webhook configuration
   - Test linking flow manually

3. **AI responses failing**
   - Check OpenRouter API key
   - Verify rate limits
   - Check model availability

4. **Database errors**
   - Check Convex deployment status
   - Verify function deployments
   - Check network connectivity

### Debug Commands

```bash
# Check bot info
curl -X GET "https://api.telegram.org/bot<BOT_TOKEN>/getMe"

# Check webhook status
curl -X GET "https://api.telegram.org/bot<BOT_TOKEN>/getWebhookInfo"

# Test health endpoint
curl https://your-domain.com/health
```

## 📚 Additional Resources

- [grammY Documentation](https://grammy.dev/)
- [Convex Documentation](https://docs.convex.dev/)
- [Clerk Documentation](https://clerk.com/docs)
- [OpenRouter API Documentation](https://openrouter.ai/docs)
- [Telegram Bot API](https://core.telegram.org/bots/api)

## 🆘 Support

If you encounter issues:

1. Check the troubleshooting section above
2. Review bot logs for error messages
3. Consult the main BonKai documentation
4. Contact the development team

## 🔮 Next Steps

After successful deployment:

1. Set up monitoring and alerting
2. Configure automatic deployments
3. Set up staging environment
4. Plan feature rollouts
5. Monitor user feedback

Happy chatting with BonKai AI! 🤖
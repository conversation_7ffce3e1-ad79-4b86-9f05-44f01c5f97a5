#!/usr/bin/env bun
// 🔒 Pre-commit Security Check
// Run this before committing to ensure no secrets are exposed

import { readFileSync, existsSync } from 'fs';
import { execSync } from 'child_process';

console.log('🔒 Pre-commit Security Check');
console.log('============================\n');

let hasErrors = false;

// 1. Check for sensitive files in git staging area
console.log('1. Checking for sensitive files...');
try {
  const stagedFiles = execSync('git diff --cached --name-only', { encoding: 'utf8' }).split('\n').filter(Boolean);
  
  const sensitivePatterns = [
    /\.env\.local$/,
    /\.env$/,
    /.*\.key$/,
    /.*\.pem$/,
    /.*secret.*$/i,
    /.*password.*$/i,
  ];

  const sensitiveFiles = stagedFiles.filter(file => 
    sensitivePatterns.some(pattern => pattern.test(file))
  );

  if (sensitiveFiles.length > 0) {
    console.log('❌ DANGER: Sensitive files found in staging area:');
    sensitiveFiles.forEach(file => console.log(`   - ${file}`));
    console.log('   Remove these files before committing!');
    hasErrors = true;
  } else {
    console.log('✅ No sensitive files in staging area');
  }
} catch (error) {
  console.log('⚠️  Could not check staged files (not in git repo?)');
}

// 2. Check for hardcoded secrets in staged files
console.log('\n2. Checking for hardcoded secrets...');
try {
  const stagedFiles = execSync('git diff --cached --name-only', { encoding: 'utf8' })
    .split('\n')
    .filter(Boolean)
    .filter(file => file.endsWith('.ts') || file.endsWith('.js') || file.endsWith('.tsx') || file.endsWith('.jsx'));

  const secretPatterns = [
    /sk_test_[a-zA-Z0-9]{48,}/g,  // Clerk secret keys
    /sk_live_[a-zA-Z0-9]{48,}/g,  // UploadThing secret keys
    /sk-or-v1-[a-zA-Z0-9]{64}/g,  // OpenRouter API keys
    /\d{10}:[a-zA-Z0-9_-]{35}/g,  // Telegram bot tokens
    /pk_test_[a-zA-Z0-9]{48,}/g,  // Clerk publishable keys (less sensitive but still check)
  ];

  let foundSecrets = false;

  for (const file of stagedFiles) {
    if (existsSync(file)) {
      const content = readFileSync(file, 'utf8');
      
      for (const pattern of secretPatterns) {
        const matches = content.match(pattern);
        if (matches) {
          console.log(`❌ DANGER: Potential secret found in ${file}:`);
          matches.forEach(match => {
            const masked = match.substring(0, 8) + '***' + match.substring(match.length - 4);
            console.log(`   - ${masked}`);
          });
          foundSecrets = true;
          hasErrors = true;
        }
      }
    }
  }

  if (!foundSecrets) {
    console.log('✅ No hardcoded secrets found in staged files');
  }
} catch (error) {
  console.log('⚠️  Could not check for hardcoded secrets:', error);
}

// 3. Verify .env.local is in .gitignore
console.log('\n3. Checking .gitignore configuration...');
if (existsSync('.gitignore')) {
  const gitignoreContent = readFileSync('.gitignore', 'utf8');
  
  const requiredIgnores = [
    '.env.local',
    '.env',
    '*.key',
    '*.pem',
  ];

  const missingIgnores = requiredIgnores.filter(pattern => 
    !gitignoreContent.includes(pattern)
  );

  if (missingIgnores.length > 0) {
    console.log('❌ Missing patterns in .gitignore:');
    missingIgnores.forEach(pattern => console.log(`   - ${pattern}`));
    hasErrors = true;
  } else {
    console.log('✅ .gitignore properly configured');
  }
} else {
  console.log('❌ .gitignore file not found');
  hasErrors = true;
}

// 4. Check if .env.local exists and warn
console.log('\n4. Checking environment file status...');
if (existsSync('.env.local')) {
  console.log('⚠️  .env.local exists - ensure it\'s not committed');
  
  // Check if it's actually ignored
  try {
    const gitStatus = execSync('git status --porcelain .env.local', { encoding: 'utf8' });
    if (gitStatus.trim()) {
      console.log('❌ DANGER: .env.local is being tracked by git!');
      hasErrors = true;
    } else {
      console.log('✅ .env.local is properly ignored by git');
    }
  } catch (error) {
    // File is ignored, which is good
    console.log('✅ .env.local is properly ignored by git');
  }
} else {
  console.log('✅ No .env.local file found');
}

// 5. Verify security fixes are in place
console.log('\n5. Verifying security fixes...');

const securityChecks = [
  {
    name: 'Middleware consolidation',
    check: () => existsSync('apps/web/middleware.ts') && !existsSync('apps/web/middleware-clerk.ts'),
    message: 'Authentication middleware consolidated'
  },
  {
    name: 'Input validation',
    check: () => existsSync('apps/web/lib/security/input-validation.ts'),
    message: 'Input validation utilities present'
  },
  {
    name: 'Security headers',
    check: () => {
      if (existsSync('apps/web/next.config.ts')) {
        const config = readFileSync('apps/web/next.config.ts', 'utf8');
        return config.includes('Content-Security-Policy');
      }
      return false;
    },
    message: 'Security headers configured'
  },
  {
    name: 'Telegram validation',
    check: () => existsSync('apps/telegram-bot/src/lib/validation.ts'),
    message: 'Telegram bot validation present'
  },
];

securityChecks.forEach(({ name, check, message }) => {
  if (check()) {
    console.log(`✅ ${message}`);
  } else {
    console.log(`❌ ${name} check failed`);
    hasErrors = true;
  }
});

// Final result
console.log('\n' + '='.repeat(40));
if (hasErrors) {
  console.log('❌ COMMIT BLOCKED: Security issues found!');
  console.log('Fix the issues above before committing.');
  process.exit(1);
} else {
  console.log('✅ SECURITY CHECK PASSED');
  console.log('Safe to commit to repository.');
  console.log('\nNext steps:');
  console.log('1. git commit -m "feat: implement comprehensive security fixes"');
  console.log('2. git push origin main');
  console.log('3. Deploy to Vercel using the deployment guide');
}
console.log('='.repeat(40));
